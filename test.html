<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>iframe跨域问题与解决方案</title>
    <style>
      :root {
        --primary-color: #3498db;
        --secondary-color: #2ecc71;
        --warning-color: #e74c3c;
        --dark-color: #2c3e50;
        --light-color: #ecf0f1;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: var(--dark-color);
        margin-bottom: 10px;
      }

      .description {
        color: #666;
        max-width: 800px;
        margin: 0 auto;
      }

      .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
      }

      .card h2 {
        color: var(--primary-color);
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--light-color);
      }

      .flex-container {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
      }

      .iframe-container {
        flex: 1;
        min-height: 400px;
      }

      .iframe-container iframe {
        width: 100%;
        height: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .info-box {
        flex: 1;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
      }

      .warning {
        background-color: #fff4f4;
        border-left: 4px solid var(--warning-color);
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
      }

      .solution {
        background-color: #f0f9ff;
        border-left: 4px solid var(--secondary-color);
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
      }

      .tabs {
        display: flex;
        margin-bottom: 20px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
      }

      .tab {
        padding: 15px 20px;
        background: #e9ecef;
        cursor: pointer;
        transition: background 0.3s;
        text-align: center;
        flex: 1;
      }

      .tab.active {
        background: var(--primary-color);
        color: white;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      pre {
        background: #2d2d2d;
        color: #f8f8f2;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', monospace;
      }

      @media (max-width: 768px) {
        .flex-container {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header>
        <h1>iframe跨域问题与解决方案</h1>
        <p class="description">
          当使用iframe嵌入不同源的页面时，由于浏览器的同源策略，会限制页面与iframe之间的交互。
          本页面将展示跨域问题及其解决方案。
        </p>
      </header>

      <div class="card">
        <h2>什么是跨域？</h2>
        <p>
          当iframe嵌入的页面与父页面在<strong>协议、域名或端口</strong>有任何不同时，就会发生跨域。
        </p>

        <div class="warning">
          <h3>跨域限制包括：</h3>
          <ul>
            <li>无法通过JavaScript访问iframe内的DOM</li>
            <li>无法读取iframe内的cookie、localStorage等数据</li>
            <li>无法捕获iframe内的JavaScript异常</li>
            <li>无法调用iframe内的函数或变量</li>
          </ul>
        </div>
      </div>

      <div class="tabs">
        <div class="tab active" onclick="switchTab(0)">问题演示</div>
        <div class="tab" onclick="switchTab(1)">解决方案</div>
        <div class="tab" onclick="switchTab(2)">代码示例</div>
      </div>

      <div class="tab-content active">
        <div class="card">
          <h2>跨域iframe演示</h2>
          <div class="flex-container">
            <div class="iframe-container">
              <iframe
                id="crossDomainIframe"
                src="https://httpbin.org/html"
              ></iframe>
            </div>
            <div class="info-box">
              <h3>跨域iframe信息</h3>
              <p>
                此iframe加载的是 https://httpbin.org/html，与当前页面不同源。
              </p>

              <div class="warning">
                <p>尝试访问iframe内容会导致错误：</p>
                <button onclick="tryAccessIframe()">尝试访问iframe内容</button>
                <p id="errorMessage"></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content">
        <div class="card">
          <h2>解决跨域通信的方法</h2>

          <div class="solution">
            <h3>1. 使用postMessage API</h3>
            <p>HTML5的postMessage API允许跨域文档之间进行安全的通信。</p>
            <ul>
              <li>父页面使用iframe.contentWindow.postMessage()发送消息</li>
              <li>
                iframe内使用window.addEventListener('message', handler)接收消息
              </li>
              <li>需要验证消息来源以确保安全</li>
            </ul>
          </div>

          <div class="solution">
            <h3>2. 设置document.domain</h3>
            <p>
              如果两个页面具有相同的基础域名（例如a.example.com和b.example.com），可以将document.domain设置为example.com。
            </p>
            <ul>
              <li>仅适用于具有相同基础域名的页面</li>
              <li>在现代浏览器中已逐渐被限制使用</li>
            </ul>
          </div>

          <div class="solution">
            <h3>3. 使用CORS（跨域资源共享）</h3>
            <p>
              通过服务器设置Access-Control-Allow-Origin响应头，允许特定域访问资源。
            </p>
            <ul>
              <li>需要服务器端配合</li>
              <li>适用于AJAX请求和某些资源的加载</li>
            </ul>
          </div>

          <div class="solution">
            <h3>4. 使用代理服务器</h3>
            <p>通过自己的服务器代理请求，避免浏览器直接跨域请求。</p>
            <ul>
              <li>完全控制跨域请求</li>
              <li>增加服务器负载</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="tab-content">
        <div class="card">
          <h2>postMessage代码示例</h2>

          <h3>父页面发送消息：</h3>
          <pre><code>// 获取iframe元素
const iframe = document.getElementById('myIframe');

// 等待iframe加载完成
iframe.onload = function() {
  // 向iframe发送消息
  iframe.contentWindow.postMessage({
    type: 'GREETING',
    data: 'Hello from parent window'
  }, 'https://iframe-domain.com');
};

// 监听来自iframe的消息
window.addEventListener('message', function(event) {
  // 验证消息来源
  if (event.origin !== 'https://iframe-domain.com') return;
  
  // 处理消息
  console.log('Received message from iframe:', event.data);
});</code></pre>

          <h3>iframe内接收和发送消息：</h3>
          <pre><code>// 监听来自父页面的消息
window.addEventListener('message', function(event) {
  // 验证消息来源
  if (event.origin !== 'https://parent-domain.com') return;
  
  // 处理消息
  console.log('Received message from parent:', event.data);
  
  // 回复消息
  event.source.postMessage({
    type: 'REPLY',
    data: 'Hello from iframe'
  }, event.origin);
});</code></pre>
        </div>
      </div>
    </div>

    <script>
      function switchTab(tabIndex) {
        // 隐藏所有标签内容
        const tabContents = document.querySelectorAll('.tab-content')
        tabContents.forEach((content) => content.classList.remove('active'))

        // 取消所有标签的激活状态
        const tabs = document.querySelectorAll('.tab')
        tabs.forEach((tab) => tab.classList.remove('active'))

        // 激活选中的标签和内容
        tabs[tabIndex].classList.add('active')
        tabContents[tabIndex].classList.add('active')
      }

      function tryAccessIframe() {
        const errorMessage = document.getElementById('errorMessage')
        try {
          const iframe = document.getElementById('crossDomainIframe')
          // 尝试访问iframe的内容，这会触发跨域错误
          const iframeContent = iframe.contentWindow.document.body.innerHTML
          errorMessage.textContent =
            '成功访问iframe内容: ' + iframeContent.substring(0, 100) + '...'
        } catch (error) {
          errorMessage.textContent = '错误: ' + error.message
        }
      }

      // 获取iframe元素
      const iframe = document.getElementById('myIframe')

      // 等待iframe加载完成
      iframe.onload = function () {
        // 向iframe发送消息
        iframe.contentWindow.postMessage(
          {
            type: 'GREETING',
            data: 'Hello from parent window',
          },
          'https://iframe-domain.com'
        )
      }

      // 监听来自iframe的消息
      window.addEventListener('message', function (event) {
        // 验证消息来源
        if (event.origin !== 'https://iframe-domain.com') return

        // 处理消息
        console.log('Received message from iframe:', event.data)
      })

      // 监听来自父页面的消息
      window.addEventListener('message', function (event) {
        // 验证消息来源
        if (event.origin !== 'https://parent-domain.com') return

        // 处理消息
        console.log('Received message from parent:', event.data)

        // 回复消息
        event.source.postMessage(
          {
            type: 'REPLY',
            data: 'Hello from iframe',
          },
          event.origin
        )
      })
    </script>
  </body>
</html>
