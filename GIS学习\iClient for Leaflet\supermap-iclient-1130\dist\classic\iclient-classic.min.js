!function(){var t={4537:function(t){!function(e){"use strict";if(e.__disableNativeFetch||!e.fetch){s.prototype.append=function(t,e){t=i(t),e=a(e);var r=this.map[t];r||(r=[],this.map[t]=r),r.push(e)},s.prototype.delete=function(t){delete this.map[i(t)]},s.prototype.get=function(t){var e=this.map[i(t)];return e?e[0]:null},s.prototype.getAll=function(t){return this.map[i(t)]||[]},s.prototype.has=function(t){return this.map.hasOwnProperty(i(t))},s.prototype.set=function(t,e){this.map[i(t)]=[a(e)]},s.prototype.forEach=function(t,e){Object.getOwnPropertyNames(this.map).forEach(function(r){this.map[r].forEach(function(n){t.call(e,n,r,this)},this)},this)};var r={blob:"FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(t){return!1}}(),formData:"FormData"in e,arrayBuffer:"ArrayBuffer"in e},n=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];p.prototype.clone=function(){return new p(this)},f.call(p.prototype),f.call(y.prototype),y.prototype.clone=function(){return new y(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new s(this.headers),url:this.url})},y.error=function(){var t=new y(null,{status:0,statusText:""});return t.type="error",t};var o=[301,302,303,307,308];y.redirect=function(t,e){if(-1===o.indexOf(e))throw new RangeError("Invalid status code");return new y(null,{status:e,headers:{location:t}})},e.Headers=s,e.Request=p,e.Response=y,e.fetch=function(t,e){return new Promise(function(n,o){var i;i=p.prototype.isPrototypeOf(t)&&!e?t:new p(t,e);var a=new XMLHttpRequest;var u=!1;function c(){if(4===a.readyState){var t=1223===a.status?204:a.status;if(t<100||t>599){if(u)return;return u=!0,void o(new TypeError("Network request failed"))}var e={status:t,statusText:a.statusText,headers:function(t){var e=new s;return t.getAllResponseHeaders().trim().split("\n").forEach(function(t){var r=t.trim().split(":"),n=r.shift().trim(),o=r.join(":").trim();e.append(n,o)}),e}(a),url:"responseURL"in a?a.responseURL:/^X-Request-URL:/m.test(a.getAllResponseHeaders())?a.getResponseHeader("X-Request-URL"):void 0},r="response"in a?a.response:a.responseText;u||(u=!0,n(new y(r,e)))}}a.onreadystatechange=c,a.onload=c,a.onerror=function(){u||(u=!0,o(new TypeError("Network request failed")))},a.open(i.method,i.url,!0);try{"include"===i.credentials&&("withCredentials"in a?a.withCredentials=!0:console&&console.warn&&console.warn("withCredentials is not supported, you can ignore this warning"))}catch(t){console&&console.warn&&console.warn("set withCredentials error:"+t)}"responseType"in a&&r.blob&&(a.responseType="blob"),i.headers.forEach(function(t,e){a.setRequestHeader(e,t)}),a.send(void 0===i._bodyInit?null:i._bodyInit)})},e.fetch.polyfill=!0,t.exports&&(t.exports=e.fetch)}function i(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(t))throw new TypeError("Invalid character in header field name");return t.toLowerCase()}function a(t){return"string"!=typeof t&&(t=String(t)),t}function s(t){this.map={},t instanceof s?t.forEach(function(t,e){this.append(e,t)},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function u(t){if(t.bodyUsed)return Promise.reject(new TypeError("Already read"));t.bodyUsed=!0}function c(t){return new Promise(function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}})}function l(t){var e=new FileReader;return e.readAsArrayBuffer(t),c(e)}function f(){return this.bodyUsed=!1,this._initBody=function(t,e){if(this._bodyInit=t,"string"==typeof t)this._bodyText=t;else if(r.blob&&Blob.prototype.isPrototypeOf(t))this._bodyBlob=t,this._options=e;else if(r.formData&&FormData.prototype.isPrototypeOf(t))this._bodyFormData=t;else if(t){if(!r.arrayBuffer||!ArrayBuffer.prototype.isPrototypeOf(t))throw new Error("unsupported BodyInit type")}else this._bodyText=""},r.blob?(this.blob=function(){var t=u(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this.blob().then(l)},this.text=function(){var t,e,r,n,o,i,a,s=u(this);if(s)return s;if(this._bodyBlob)return t=this._bodyBlob,e=this._options,r=new FileReader,n=e.headers.map["content-type"]?e.headers.map["content-type"].toString():"",o=/charset\=[0-9a-zA-Z\-\_]*;?/,i=t.type.match(o)||n.match(o),a=[t],i&&a.push(i[0].replace(/^charset\=/,"").replace(/;$/,"")),r.readAsText.apply(r,a),c(r);if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)}):this.text=function(){var t=u(this);return t||Promise.resolve(this._bodyText)},r.formData&&(this.formData=function(){return this.text().then(h)}),this.json=function(){return this.text().then(JSON.parse)},this}function p(t,e){var r,o,i=(e=e||{}).body;if(p.prototype.isPrototypeOf(t)){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new s(t.headers)),this.method=t.method,this.mode=t.mode,i||(i=t._bodyInit,t.bodyUsed=!0)}else this.url=t;if(this.credentials=e.credentials||this.credentials||"omit",!e.headers&&this.headers||(this.headers=new s(e.headers)),this.method=(r=e.method||this.method||"GET",o=r.toUpperCase(),n.indexOf(o)>-1?o:r),this.mode=e.mode||this.mode||null,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(i,e)}function h(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(o))}}),e}function y(t,e){e||(e={}),this._initBody(t,e),this.type="default",this.status=e.status,this.ok=this.status>=200&&this.status<300,this.statusText=e.statusText,this.headers=e.headers instanceof s?e.headers:new s(e.headers),this.url=e.url||""}}("undefined"!=typeof self?self:this)},9005:function(t,e){var r,n,o;n=[e,t],void 0===(o="function"==typeof(r=function(t,e){"use strict";var r={timeout:5e3,jsonpCallback:"callback",jsonpCallbackFunction:null};function n(t){try{delete window[t]}catch(e){window[t]=void 0}}function o(t){var e=document.getElementById(t);e&&document.getElementsByTagName("head")[0].removeChild(e)}e.exports=function(t){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],i=t,a=e.timeout||r.timeout,s=e.jsonpCallback||r.jsonpCallback,u=void 0;return new Promise(function(r,c){var l=e.jsonpCallbackFunction||"jsonp_"+Date.now()+"_"+Math.ceil(1e5*Math.random()),f=s+"_"+l;window[l]=function(t){r({ok:!0,json:function(){return Promise.resolve(t)}}),u&&clearTimeout(u),o(f),n(l)},i+=-1===i.indexOf("?")?"?":"&";var p=document.createElement("script");p.setAttribute("src",""+i+s+"="+l),e.charset&&p.setAttribute("charset",e.charset),p.id=f,document.getElementsByTagName("head")[0].appendChild(p),u=setTimeout(function(){c(new Error("JSONP request to "+t+" timed out")),n(l),o(f),window[l]=function(){n(l)}},a),p.onerror=function(){c(new Error("JSONP request to "+t+" failed")),n(l),o(f),u&&clearTimeout(u)}})}})?r.apply(e,n):r)||(t.exports=o)},3819:function(t,e,r){var n,o,i;function a(t){"@babel/helpers - typeof";return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}i=function(){"use strict";function t(t){var e=this.constructor;return this.then(function(r){return e.resolve(t()).then(function(){return r})},function(r){return e.resolve(t()).then(function(){return e.reject(r)})})}function e(t){return new this(function(e,r){if(!t||void 0===t.length)return r(new TypeError(a(t)+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var n=Array.prototype.slice.call(t);if(0===n.length)return e([]);var o=n.length;function i(t,r){if(r&&("object"===a(r)||"function"==typeof r)){var s=r.then;if("function"==typeof s)return void s.call(r,function(e){i(t,e)},function(r){n[t]={status:"rejected",reason:r},0==--o&&e(n)})}n[t]={status:"fulfilled",value:r},0==--o&&e(n)}for(var s=0;s<n.length;s++)i(s,n[s])})}var n=setTimeout;function o(t){return Boolean(t&&void 0!==t.length)}function i(){}function s(t){if(!(this instanceof s))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],p(t,this)}function u(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,s._immediateFn(function(){var r=1===t._state?e.onFulfilled:e.onRejected;if(null!==r){var n;try{n=r(t._value)}catch(t){return void l(e.promise,t)}c(e.promise,n)}else(1===t._state?c:l)(e.promise,t._value)})):t._deferreds.push(e)}function c(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"===a(e)||"function"==typeof e)){var r=e.then;if(e instanceof s)return t._state=3,t._value=e,void f(t);if("function"==typeof r)return void p((n=r,o=e,function(){n.apply(o,arguments)}),t)}t._state=1,t._value=e,f(t)}catch(e){l(t,e)}var n,o}function l(t,e){t._state=2,t._value=e,f(t)}function f(t){2===t._state&&0===t._deferreds.length&&s._immediateFn(function(){t._handled||s._unhandledRejectionFn(t._value)});for(var e=0,r=t._deferreds.length;e<r;e++)u(t,t._deferreds[e]);t._deferreds=null}function p(t,e){var r=!1;try{t(function(t){r||(r=!0,c(e,t))},function(t){r||(r=!0,l(e,t))})}catch(t){if(r)return;r=!0,l(e,t)}}s.prototype.catch=function(t){return this.then(null,t)},s.prototype.then=function(t,e){var r=new this.constructor(i);return u(this,new function(t,e,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=r}(t,e,r)),r},s.prototype.finally=t,s.all=function(t){return new s(function(e,r){if(!o(t))return r(new TypeError("Promise.all accepts an array"));var n=Array.prototype.slice.call(t);if(0===n.length)return e([]);var i=n.length;function s(t,o){try{if(o&&("object"===a(o)||"function"==typeof o)){var u=o.then;if("function"==typeof u)return void u.call(o,function(e){s(t,e)},r)}n[t]=o,0==--i&&e(n)}catch(t){r(t)}}for(var u=0;u<n.length;u++)s(u,n[u])})},s.allSettled=e,s.resolve=function(t){return t&&"object"===a(t)&&t.constructor===s?t:new s(function(e){e(t)})},s.reject=function(t){return new s(function(e,r){r(t)})},s.race=function(t){return new s(function(e,r){if(!o(t))return r(new TypeError("Promise.race accepts an array"));for(var n=0,i=t.length;n<i;n++)s.resolve(t[n]).then(e,r)})},s._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){n(t,0)},s._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var h=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}();"function"!=typeof h.Promise?h.Promise=s:(h.Promise.prototype.finally||(h.Promise.prototype.finally=t),h.Promise.allSettled||(h.Promise.allSettled=e))},"object"===a(e)?i():void 0===(o="function"==typeof(n=i)?n.call(e,r,e,t):n)||(t.exports=o)},6369:function(t,e,r){var n,o;function i(t){"@babel/helpers - typeof";return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t=r.nmd(t),function(a,s){"use strict";"object"===i(t)&&t.exports?t.exports=s():void 0===(o="function"==typeof(n=s)?n.call(e,r,e,t):n)||(t.exports=o)}(0,function(t){"use strict";var e=t&&t.IPv6;return{best:function(t){var e,r,n=t.toLowerCase().split(":"),o=n.length,i=8;for(""===n[0]&&""===n[1]&&""===n[2]?(n.shift(),n.shift()):""===n[0]&&""===n[1]?n.shift():""===n[o-1]&&""===n[o-2]&&n.pop(),-1!==n[(o=n.length)-1].indexOf(".")&&(i=7),e=0;e<o&&""!==n[e];e++);if(e<i)for(n.splice(e,1,"0000");n.length<i;)n.splice(e,0,"0000");for(var a=0;a<i;a++){r=n[a].split("");for(var s=0;s<3&&"0"===r[0]&&r.length>1;s++)r.splice(0,1);n[a]=r.join("")}var u=-1,c=0,l=0,f=-1,p=!1;for(a=0;a<i;a++)p?"0"===n[a]?l+=1:(p=!1,l>c&&(u=f,c=l)):"0"===n[a]&&(p=!0,f=a,l=1);l>c&&(u=f,c=l),c>1&&n.splice(u,c,""),o=n.length;var h="";for(""===n[0]&&(h=":"),a=0;a<o&&(h+=n[a],a!==o-1);a++)h+=":";return""===n[o-1]&&(h+=":"),h},noConflict:function(){return t.IPv6===this&&(t.IPv6=e),this}}})},7011:function(t,e,r){var n,o;function i(t){"@babel/helpers - typeof";return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t=r.nmd(t),function(a,s){"use strict";"object"===i(t)&&t.exports?t.exports=s():void 0===(o="function"==typeof(n=s)?n.call(e,r,e,t):n)||(t.exports=o)}(0,function(t){"use strict";var e=t&&t.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;var n=t.lastIndexOf(".",e-1);if(n<=0||n>=e-1)return!1;var o=r.list[t.slice(e+1)];return!!o&&o.indexOf(" "+t.slice(n+1,e)+" ")>=0},is:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;if(t.lastIndexOf(".",e-1)>=0)return!1;var n=r.list[t.slice(e+1)];return!!n&&n.indexOf(" "+t.slice(0,e)+" ")>=0},get:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return null;var n=t.lastIndexOf(".",e-1);if(n<=0||n>=e-1)return null;var o=r.list[t.slice(e+1)];return o?o.indexOf(" "+t.slice(n+1,e)+" ")<0?null:t.slice(n+1):null},noConflict:function(){return t.SecondLevelDomains===this&&(t.SecondLevelDomains=e),this}};return r})},9216:function(t,e,r){var n,o,i;function a(t){"@babel/helpers - typeof";return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t=r.nmd(t),function(s,u){"use strict";"object"===a(t)&&t.exports?t.exports=u(r(2323),r(6369),r(7011)):(o=[r(2323),r(6369),r(7011)],void 0===(i="function"==typeof(n=u)?n.apply(e,o):n)||(t.exports=i))}(0,function(t,e,r,n){"use strict";var o=n&&n.URI;function i(t,e){var r=arguments.length>=1,n=arguments.length>=2;if(!(this instanceof i))return r?n?new i(t,e):new i(t):new i;if(void 0===t){if(r)throw new TypeError("undefined is not a valid argument for URI");t="undefined"!=typeof location?location.href+"":""}if(null===t&&r)throw new TypeError("null is not a valid argument for URI");return this.href(t),void 0!==e?this.absoluteTo(e):this}i.version="1.19.11";var s=i.prototype,u=Object.prototype.hasOwnProperty;function c(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(t){return void 0===t?"Undefined":String(Object.prototype.toString.call(t)).slice(8,-1)}function f(t){return"Array"===l(t)}function p(t,e){var r,n,o={};if("RegExp"===l(e))o=null;else if(f(e))for(r=0,n=e.length;r<n;r++)o[e[r]]=!0;else o[e]=!0;for(r=0,n=t.length;r<n;r++){(o&&void 0!==o[t[r]]||!o&&e.test(t[r]))&&(t.splice(r,1),n--,r--)}return t}function h(t,e){var r,n;if(f(e)){for(r=0,n=e.length;r<n;r++)if(!h(t,e[r]))return!1;return!0}var o=l(e);for(r=0,n=t.length;r<n;r++)if("RegExp"===o){if("string"==typeof t[r]&&t[r].match(e))return!0}else if(t[r]===e)return!0;return!1}function y(t,e){if(!f(t)||!f(e))return!1;if(t.length!==e.length)return!1;t.sort(),e.sort();for(var r=0,n=t.length;r<n;r++)if(t[r]!==e[r])return!1;return!0}function d(t){return t.replace(/^\/+|\/+$/g,"")}function m(t){return escape(t)}function v(t){return encodeURIComponent(t).replace(/[!'()*]/g,m).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(t){if(t&&t.nodeName){var e=t.nodeName.toLowerCase();if("input"!==e||"image"===t.type)return i.domAttributes[e]}},i.encode=v,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=v,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(t,e){var r=i.encode(t+"");return void 0===e&&(e=i.escapeQuerySpace),e?r.replace(/%20/g,"+"):r},i.decodeQuery=function(t,e){t+="",void 0===e&&(e=i.escapeQuerySpace);try{return i.decode(e?t.replace(/\+/g,"%20"):t)}catch(e){return t}};var b,g={encode:"encode",decode:"decode"},S=function(t,e){return function(r){try{return i[e](r+"").replace(i.characters[t][e].expression,function(r){return i.characters[t][e].map[r]})}catch(t){return r}}};for(b in g)i[b+"PathSegment"]=S("pathname",g[b]),i[b+"UrnPathSegment"]=S("urnpath",g[b]);var w=function(t,e,r){return function(n){var o;o=r?function(t){return i[e](i[r](t))}:i[e];for(var a=(n+"").split(t),s=0,u=a.length;s<u;s++)a[s]=o(a[s]);return a.join(t)}};function O(t){return function(e,r){return void 0===e?this._parts[t]||"":(this._parts[t]=e||null,this.build(!r),this)}}function _(t,e){return function(r,n){return void 0===r?this._parts[t]||"":(null!==r&&(r+="").charAt(0)===e&&(r=r.substring(1)),this._parts[t]=r,this.build(!n),this)}}i.decodePath=w("/","decodePathSegment"),i.decodeUrnPath=w(":","decodeUrnPathSegment"),i.recodePath=w("/","encodePathSegment","decode"),i.recodeUrnPath=w(":","encodeUrnPathSegment","decode"),i.encodeReserved=S("reserved","encode"),i.parse=function(t,e){var r;return e||(e={preventInvalidHostname:i.preventInvalidHostname}),(r=(t=(t=t.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(e.fragment=t.substring(r+1)||null,t=t.substring(0,r)),(r=t.indexOf("?"))>-1&&(e.query=t.substring(r+1)||null,t=t.substring(0,r)),"//"===(t=(t=t.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(e.protocol=null,t=t.substring(2),t=i.parseAuthority(t,e)):(r=t.indexOf(":"))>-1&&(e.protocol=t.substring(0,r)||null,e.protocol&&!e.protocol.match(i.protocol_expression)?e.protocol=void 0:"//"===t.substring(r+1,r+3).replace(/\\/g,"/")?(t=t.substring(r+3),t=i.parseAuthority(t,e)):(t=t.substring(r+1),e.urn=!0)),e.path=t,e},i.parseHost=function(t,e){t||(t="");var r,n,o=(t=t.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=t.length),"["===t.charAt(0))r=t.indexOf("]"),e.hostname=t.substring(1,r)||null,e.port=t.substring(r+2,o)||null,"/"===e.port&&(e.port=null);else{var a=t.indexOf(":"),s=t.indexOf("/"),u=t.indexOf(":",a+1);-1!==u&&(-1===s||u<s)?(e.hostname=t.substring(0,o)||null,e.port=null):(n=t.substring(0,o).split(":"),e.hostname=n[0]||null,e.port=n[1]||null)}return e.hostname&&"/"!==t.substring(o).charAt(0)&&(o++,t="/"+t),e.preventInvalidHostname&&i.ensureValidHostname(e.hostname,e.protocol),e.port&&i.ensureValidPort(e.port),t.substring(o)||"/"},i.parseAuthority=function(t,e){return t=i.parseUserinfo(t,e),i.parseHost(t,e)},i.parseUserinfo=function(t,e){var r=t;-1!==t.indexOf("\\")&&(t=t.replace(/\\/g,"/"));var n,o=t.indexOf("/"),a=t.lastIndexOf("@",o>-1?o:t.length-1);return a>-1&&(-1===o||a<o)?(n=t.substring(0,a).split(":"),e.username=n[0]?i.decode(n[0]):null,n.shift(),e.password=n[0]?i.decode(n.join(":")):null,t=r.substring(a+1)):(e.username=null,e.password=null),t},i.parseQuery=function(t,e){if(!t)return{};if(!(t=t.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,o,a={},s=t.split("&"),c=s.length,l=0;l<c;l++)r=s[l].split("="),n=i.decodeQuery(r.shift(),e),o=r.length?i.decodeQuery(r.join("="),e):null,"__proto__"!==n&&(u.call(a,n)?("string"!=typeof a[n]&&null!==a[n]||(a[n]=[a[n]]),a[n].push(o)):a[n]=o);return a},i.build=function(t){var e="",r=!1;return t.protocol&&(e+=t.protocol+":"),t.urn||!e&&!t.hostname||(e+="//",r=!0),e+=i.buildAuthority(t)||"","string"==typeof t.path&&("/"!==t.path.charAt(0)&&r&&(e+="/"),e+=t.path),"string"==typeof t.query&&t.query&&(e+="?"+t.query),"string"==typeof t.fragment&&t.fragment&&(e+="#"+t.fragment),e},i.buildHost=function(t){var e="";return t.hostname?(i.ip6_expression.test(t.hostname)?e+="["+t.hostname+"]":e+=t.hostname,t.port&&(e+=":"+t.port),e):""},i.buildAuthority=function(t){return i.buildUserinfo(t)+i.buildHost(t)},i.buildUserinfo=function(t){var e="";return t.username&&(e+=i.encode(t.username)),t.password&&(e+=":"+i.encode(t.password)),e&&(e+="@"),e},i.buildQuery=function(t,e,r){var n,o,a,s,c="";for(o in t)if("__proto__"!==o&&u.call(t,o))if(f(t[o]))for(n={},a=0,s=t[o].length;a<s;a++)void 0!==t[o][a]&&void 0===n[t[o][a]+""]&&(c+="&"+i.buildQueryParameter(o,t[o][a],r),!0!==e&&(n[t[o][a]+""]=!0));else void 0!==t[o]&&(c+="&"+i.buildQueryParameter(o,t[o],r));return c.substring(1)},i.buildQueryParameter=function(t,e,r){return i.encodeQuery(t,r)+(null!==e?"="+i.encodeQuery(e,r):"")},i.addQuery=function(t,e,r){if("object"===a(e))for(var n in e)u.call(e,n)&&i.addQuery(t,n,e[n]);else{if("string"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===t[e])return void(t[e]=r);"string"==typeof t[e]&&(t[e]=[t[e]]),f(r)||(r=[r]),t[e]=(t[e]||[]).concat(r)}},i.setQuery=function(t,e,r){if("object"===a(e))for(var n in e)u.call(e,n)&&i.setQuery(t,n,e[n]);else{if("string"!=typeof e)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");t[e]=void 0===r?null:r}},i.removeQuery=function(t,e,r){var n,o,s;if(f(e))for(n=0,o=e.length;n<o;n++)t[e[n]]=void 0;else if("RegExp"===l(e))for(s in t)e.test(s)&&(t[s]=void 0);else if("object"===a(e))for(s in e)u.call(e,s)&&i.removeQuery(t,s,e[s]);else{if("string"!=typeof e)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===l(r)?!f(t[e])&&r.test(t[e])?t[e]=void 0:t[e]=p(t[e],r):t[e]!==String(r)||f(r)&&1!==r.length?f(t[e])&&(t[e]=p(t[e],r)):t[e]=void 0:t[e]=void 0}},i.hasQuery=function(t,e,r,n){switch(l(e)){case"String":break;case"RegExp":for(var o in t)if(u.call(t,o)&&e.test(o)&&(void 0===r||i.hasQuery(t,o,r)))return!0;return!1;case"Object":for(var a in e)if(u.call(e,a)&&!i.hasQuery(t,a,e[a]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(r)){case"Undefined":return e in t;case"Boolean":return r===Boolean(f(t[e])?t[e].length:t[e]);case"Function":return!!r(t[e],e,t);case"Array":return!!f(t[e])&&(n?h:y)(t[e],r);case"RegExp":return f(t[e])?!!n&&h(t[e],r):Boolean(t[e]&&t[e].match(r));case"Number":r=String(r);case"String":return f(t[e])?!!n&&h(t[e],r):t[e]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var t=[],e=[],r=0,n=0;n<arguments.length;n++){var o=new i(arguments[n]);t.push(o);for(var a=o.segment(),s=0;s<a.length;s++)"string"==typeof a[s]&&e.push(a[s]),a[s]&&r++}if(!e.length||!r)return new i("");var u=new i("").segment(e);return""!==t[0].path()&&"/"!==t[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(t,e){var r,n=Math.min(t.length,e.length);for(r=0;r<n;r++)if(t.charAt(r)!==e.charAt(r)){r--;break}return r<1?t.charAt(0)===e.charAt(0)&&"/"===t.charAt(0)?"/":"":("/"===t.charAt(r)&&"/"===e.charAt(r)||(r=t.substring(0,r).lastIndexOf("/")),t.substring(0,r+1))},i.withinString=function(t,e,r){r||(r={});var n=r.start||i.findUri.start,o=r.end||i.findUri.end,a=r.trim||i.findUri.trim,s=r.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var c=n.exec(t);if(!c)break;var l=c.index;if(r.ignoreHtml){var f=t.slice(Math.max(l-3,0),l);if(f&&u.test(f))continue}for(var p=l+t.slice(l).search(o),h=t.slice(l,p),y=-1;;){var d=s.exec(h);if(!d)break;var m=d.index+d[0].length;y=Math.max(y,m)}if(!((h=y>-1?h.slice(0,y)+h.slice(y).replace(a,""):h.replace(a,"")).length<=c[0].length||r.ignore&&r.ignore.test(h))){var v=e(h,l,p=l+h.length,t);void 0!==v?(v=String(v),t=t.slice(0,l)+v+t.slice(p),n.lastIndex=l+v.length):n.lastIndex=p}}return n.lastIndex=0,t},i.ensureValidHostname=function(e,r){var n=!!e,o=!1;if(!!r&&(o=h(i.hostProtocols,r)),o&&!n)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(e&&e.match(i.invalid_hostname_characters)){if(!t)throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(t.toASCII(e).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(t){if(t){var e=Number(t);if(!(/^[0-9]+$/.test(e)&&e>0&&e<65536))throw new TypeError('Port "'+t+'" is not a valid port')}},i.noConflict=function(t){if(t){var e={URI:this.noConflict()};return n.URITemplate&&"function"==typeof n.URITemplate.noConflict&&(e.URITemplate=n.URITemplate.noConflict()),n.IPv6&&"function"==typeof n.IPv6.noConflict&&(e.IPv6=n.IPv6.noConflict()),n.SecondLevelDomains&&"function"==typeof n.SecondLevelDomains.noConflict&&(e.SecondLevelDomains=n.SecondLevelDomains.noConflict()),e}return n.URI===this&&(n.URI=o),this},s.build=function(t){return!0===t?this._deferred_build=!0:(void 0===t||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=O("protocol"),s.username=O("username"),s.password=O("password"),s.hostname=O("hostname"),s.port=O("port"),s.query=_("query","?"),s.fragment=_("fragment","#"),s.search=function(t,e){var r=this.query(t,e);return"string"==typeof r&&r.length?"?"+r:r},s.hash=function(t,e){var r=this.fragment(t,e);return"string"==typeof r&&r.length?"#"+r:r},s.pathname=function(t,e){if(void 0===t||!0===t){var r=this._parts.path||(this._parts.hostname?"/":"");return t?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=t?i.recodeUrnPath(t):"":this._parts.path=t?i.recodePath(t):"/",this.build(!e),this},s.path=s.pathname,s.href=function(t,e){var r;if(void 0===t)return this.toString();this._string="",this._parts=i._parts();var n=t instanceof i,o="object"===a(t)&&(t.hostname||t.path||t.pathname);t.nodeName&&(t=t[i.getDomAttribute(t)]||"",o=!1);if(!n&&o&&void 0!==t.pathname&&(t=t.toString()),"string"==typeof t||t instanceof String)this._parts=i.parse(String(t),this._parts);else{if(!n&&!o)throw new TypeError("invalid input");var s=n?t._parts:t;for(r in s)"query"!==r&&u.call(this._parts,r)&&(this._parts[r]=s[r]);s.query&&this.query(s.query,!1)}return this.build(!e),this},s.is=function(t){var e=!1,n=!1,o=!1,a=!1,s=!1,u=!1,c=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,n=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),s=(a=!(e=n||o))&&r&&r.has(this._parts.hostname),u=a&&i.idn_expression.test(this._parts.hostname),c=a&&i.punycode_expression.test(this._parts.hostname)),t.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return a;case"sld":return s;case"ip":return e;case"ip4":case"ipv4":case"inet4":return n;case"ip6":case"ipv6":case"inet6":return o;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return c}return null};var P=s.protocol,E=s.port,k=s.hostname;s.protocol=function(t,e){if(t&&!(t=t.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+t+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return P.call(this,t,e)},s.scheme=s.protocol,s.port=function(t,e){return this._parts.urn?void 0===t?"":this:(void 0!==t&&(0===t&&(t=null),t&&(":"===(t+="").charAt(0)&&(t=t.substring(1)),i.ensureValidPort(t))),E.call(this,t,e))},s.hostname=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0!==t){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(t,r))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');t=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(t,this._parts.protocol)}return k.call(this,t,e)},s.origin=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var n=i(t);return this.protocol(n.protocol()).authority(n.authority()).build(!e),this},s.host=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},s.authority=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},s.userinfo=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==t[t.length-1]&&(t+="@"),i.parseUserinfo(t,this._parts),this.build(!e),this},s.resource=function(t,e){var r;return void 0===t?this.path()+this.search()+this.hash():(r=i.parse(t),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!e),this)},s.subdomain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,n),a=new RegExp("^"+c(o));if(t&&"."!==t.charAt(t.length-1)&&(t+="."),-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");return t&&i.ensureValidHostname(t,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(a,t),this.build(!e),this},s.domain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(e).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!t)throw new TypeError("cannot set domain empty");if(-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(t,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=t;else{var o=new RegExp(c(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,t)}return this.build(!e),this},s.tld=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(n+1);return!0!==e&&r&&r.list[o.toLowerCase()]&&r.get(this._parts.hostname)||o}var i;if(!t)throw new TypeError("cannot set TLD empty");if(t.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(t))throw new TypeError('TLD "'+t+'" contains characters other than [A-Z0-9]');i=new RegExp(c(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(c(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}return this.build(!e),this},s.directory=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return t?i.decodePath(n):n}var o=this._parts.path.length-this.filename().length,a=this._parts.path.substring(0,o),s=new RegExp("^"+c(a));return this.is("relative")||(t||(t="/"),"/"!==t.charAt(0)&&(t="/"+t)),t&&"/"!==t.charAt(t.length-1)&&(t+="/"),t=i.recodePath(t),this._parts.path=this._parts.path.replace(s,t),this.build(!e),this},s.filename=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("string"!=typeof t){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return t?i.decodePathSegment(n):n}var o=!1;"/"===t.charAt(0)&&(t=t.substring(1)),t.match(/\.?\//)&&(o=!0);var a=new RegExp(c(this.filename())+"$");return t=i.recodePath(t),this._parts.path=this._parts.path.replace(a,t),o?this.normalizePath(e):this.build(!e),this},s.suffix=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,o=this.filename(),a=o.lastIndexOf(".");return-1===a?"":(r=o.substring(a+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",t?i.decodePathSegment(n):n)}"."===t.charAt(0)&&(t=t.substring(1));var s,u=this.suffix();if(u)s=t?new RegExp(c(u)+"$"):new RegExp(c("."+u)+"$");else{if(!t)return this;this._parts.path+="."+i.recodePath(t)}return s&&(t=i.recodePath(t),this._parts.path=this._parts.path.replace(s,t)),this.build(!e),this},s.segment=function(t,e,r){var n=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),a=o.split(n);if(void 0!==t&&"number"!=typeof t&&(r=e,e=t,t=void 0),void 0!==t&&"number"!=typeof t)throw new Error('Bad segment "'+t+'", must be 0-based integer');if(i&&a.shift(),t<0&&(t=Math.max(a.length+t,0)),void 0===e)return void 0===t?a:a[t];if(null===t||void 0===a[t])if(f(e)){a=[];for(var s=0,u=e.length;s<u;s++)(e[s].length||a.length&&a[a.length-1].length)&&(a.length&&!a[a.length-1].length&&a.pop(),a.push(d(e[s])))}else(e||"string"==typeof e)&&(e=d(e),""===a[a.length-1]?a[a.length-1]=e:a.push(e));else e?a[t]=d(e):a.splice(t,1);return i&&a.unshift(""),this.path(a.join(n),r)},s.segmentCoded=function(t,e,r){var n,o,a;if("number"!=typeof t&&(r=e,e=t,t=void 0),void 0===e){if(f(n=this.segment(t,e,r)))for(o=0,a=n.length;o<a;o++)n[o]=i.decode(n[o]);else n=void 0!==n?i.decode(n):void 0;return n}if(f(e))for(o=0,a=e.length;o<a;o++)e[o]=i.encode(e[o]);else e="string"==typeof e||e instanceof String?i.encode(e):e;return this.segment(t,e,r)};var j=s.query;return s.query=function(t,e){if(!0===t)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof t){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=t.call(this,r);return this._parts.query=i.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this}return void 0!==t&&"string"!=typeof t?(this._parts.query=i.buildQuery(t,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this):j.call(this,t,e)},s.setQuery=function(t,e,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof t||t instanceof String)n[t]=void 0!==e?e:null;else{if("object"!==a(t))throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in t)u.call(t,o)&&(n[o]=t[o])}return this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.addQuery=function(t,e,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(n,t,void 0===e?null:e),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.removeQuery=function(t,e,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(n,t,e),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.hasQuery=function(t,e,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(n,t,e,r)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(t){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!t)),this},s.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&t?this._parts.hostname=t.toASCII(this._parts.hostname):this.is("IPv6")&&e&&(this._parts.hostname=e.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},s.normalizePort=function(t){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!t)),this},s.normalizePath=function(t){var e,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!t),this;if("/"===this._parts.path)return this;var n,o,a="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(e=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),e&&(a=r.substring(1).match(/^(\.\.\/)+/)||"")&&(a=a[0]);-1!==(n=r.search(/\/\.\.(\/|$)/));)0!==n?(-1===(o=r.substring(0,n).lastIndexOf("/"))&&(o=n),r=r.substring(0,o)+r.substring(n+3)):r=r.substring(3);return e&&this.is("relative")&&(r=a+r.substring(1)),this._parts.path=r,this.build(!t),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(t){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!t)),this},s.normalizeFragment=function(t){return this._parts.fragment||(this._parts.fragment=null,this.build(!t)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var t=i.encode,e=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},s.unicode=function(){var t=i.encode,e=i.decode;i.encode=v,i.decode=unescape;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},s.readable=function(){var e=this.clone();e.username("").password("").normalize();var r="";if(e._parts.protocol&&(r+=e._parts.protocol+"://"),e._parts.hostname&&(e.is("punycode")&&t?(r+=t.toUnicode(e._parts.hostname),e._parts.port&&(r+=":"+e._parts.port)):r+=e.host()),e._parts.hostname&&e._parts.path&&"/"!==e._parts.path.charAt(0)&&(r+="/"),r+=e.path(!0),e._parts.query){for(var n="",o=0,a=e._parts.query.split("&"),s=a.length;o<s;o++){var u=(a[o]||"").split("=");n+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(n+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+n.substring(1)}return r+=i.decodeQuery(e.hash(),!0)},s.absoluteTo=function(t){var e,r,n,o=this.clone(),a=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t instanceof i||(t=new i(t)),o._parts.protocol)return o;if(o._parts.protocol=t._parts.protocol,this._parts.hostname)return o;for(r=0;n=a[r];r++)o._parts[n]=t._parts[n];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(e=(e=t.directory())||(0===t.path().indexOf("/")?"/":""),o._parts.path=(e?e+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=t._parts.path,o._parts.query||(o._parts.query=t._parts.query)),o.build(),o},s.relativeTo=function(t){var e,r,n,o,a,s=this.clone().normalize();if(s._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t=new i(t).normalize(),e=s._parts,r=t._parts,o=s.path(),a=t.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==a.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(e.protocol===r.protocol&&(e.protocol=null),e.username!==r.username||e.password!==r.password)return s.build();if(null!==e.protocol||null!==e.username||null!==e.password)return s.build();if(e.hostname!==r.hostname||e.port!==r.port)return s.build();if(e.hostname=null,e.port=null,o===a)return e.path="",s.build();if(!(n=i.commonPath(o,a)))return s.build();var u=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return e.path=u+e.path.substring(n.length)||"./",s.build()},s.equals=function(t){var e,r,n,o,a,s=this.clone(),c=new i(t),l={};if(s.normalize(),c.normalize(),s.toString()===c.toString())return!0;if(n=s.query(),o=c.query(),s.query(""),c.query(""),s.toString()!==c.toString())return!1;if(n.length!==o.length)return!1;for(a in e=i.parseQuery(n,this._parts.escapeQuerySpace),r=i.parseQuery(o,this._parts.escapeQuerySpace),e)if(u.call(e,a)){if(f(e[a])){if(!y(e[a],r[a]))return!1}else if(e[a]!==r[a])return!1;l[a]=!0}for(a in r)if(u.call(r,a)&&!l[a])return!1;return!0},s.preventInvalidHostname=function(t){return this._parts.preventInvalidHostname=!!t,this},s.duplicateQueryParameters=function(t){return this._parts.duplicateQueryParameters=!!t,this},s.escapeQuerySpace=function(t){return this._parts.escapeQuerySpace=!!t,this},i})},2323:function(t,e,r){var n;function o(t){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t=r.nmd(t),function(i){var a="object"==o(e)&&e&&!e.nodeType&&e,s="object"==o(t)&&t&&!t.nodeType&&t,u="object"==(void 0===r.g?"undefined":o(r.g))&&r.g;u.global!==u&&u.window!==u&&u.self!==u||(i=u);var c,l,f=2147483647,p=36,h=1,y=26,d=38,m=700,v=72,b=128,g="-",S=/^xn--/,w=/[^\x20-\x7E]/,O=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},P=p-h,E=Math.floor,k=String.fromCharCode;function j(t){throw new RangeError(_[t])}function x(t,e){for(var r=t.length,n=[];r--;)n[r]=e(t[r]);return n}function T(t,e){var r=t.split("@"),n="";return r.length>1&&(n=r[0]+"@",t=r[1]),n+x((t=t.replace(O,".")).split("."),e).join(".")}function C(t){for(var e,r,n=[],o=0,i=t.length;o<i;)(e=t.charCodeAt(o++))>=55296&&e<=56319&&o<i?56320==(64512&(r=t.charCodeAt(o++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),o--):n.push(e);return n}function R(t){return x(t,function(t){var e="";return t>65535&&(e+=k((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+=k(t)}).join("")}function A(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function N(t,e,r){var n=0;for(t=r?E(t/m):t>>1,t+=E(t/e);t>P*y>>1;n+=p)t=E(t/P);return E(n+(P+1)*t/(t+d))}function I(t){var e,r,n,o,i,a,s,u,c,l,d,m=[],S=t.length,w=0,O=b,_=v;for((r=t.lastIndexOf(g))<0&&(r=0),n=0;n<r;++n)t.charCodeAt(n)>=128&&j("not-basic"),m.push(t.charCodeAt(n));for(o=r>0?r+1:0;o<S;){for(i=w,a=1,s=p;o>=S&&j("invalid-input"),((u=(d=t.charCodeAt(o++))-48<10?d-22:d-65<26?d-65:d-97<26?d-97:p)>=p||u>E((f-w)/a))&&j("overflow"),w+=u*a,!(u<(c=s<=_?h:s>=_+y?y:s-_));s+=p)a>E(f/(l=p-c))&&j("overflow"),a*=l;_=N(w-i,e=m.length+1,0==i),E(w/e)>f-O&&j("overflow"),O+=E(w/e),w%=e,m.splice(w++,0,O)}return R(m)}function D(t){var e,r,n,o,i,a,s,u,c,l,d,m,S,w,O,_=[];for(m=(t=C(t)).length,e=b,r=0,i=v,a=0;a<m;++a)(d=t[a])<128&&_.push(k(d));for(n=o=_.length,o&&_.push(g);n<m;){for(s=f,a=0;a<m;++a)(d=t[a])>=e&&d<s&&(s=d);for(s-e>E((f-r)/(S=n+1))&&j("overflow"),r+=(s-e)*S,e=s,a=0;a<m;++a)if((d=t[a])<e&&++r>f&&j("overflow"),d==e){for(u=r,c=p;!(u<(l=c<=i?h:c>=i+y?y:c-i));c+=p)O=u-l,w=p-l,_.push(k(A(l+O%w,0))),u=E(O/w);_.push(k(A(u,0))),i=N(r,S,n==o),r=0,++n}++r,++e}return _.join("")}if(c={version:"1.3.2",ucs2:{decode:C,encode:R},decode:I,encode:D,toASCII:function(t){return T(t,function(t){return w.test(t)?"xn--"+D(t):t})},toUnicode:function(t){return T(t,function(t){return S.test(t)?I(t.slice(4).toLowerCase()):t})}},"object"==o(r.amdO)&&r.amdO)void 0===(n=function(){return c}.call(e,r,e,t))||(t.exports=n);else if(a&&s)if(t.exports==a)s.exports=c;else for(l in c)c.hasOwnProperty(l)&&(a[l]=c[l]);else i.punycode=c}(this)},4257:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function i(t){var e=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n(e)?e:e+""}var a=function(){function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.x=e?parseFloat(e):0,this.y=r?parseFloat(r):0,this.mode=n,this.CLASS_NAME="SuperMap.Pixel"}return e=t,(r=[{key:"toString",value:function(){return"x="+this.x+",y="+this.y}},{key:"clone",value:function(){return new t(this.x,this.y,this.mode)}},{key:"equals",value:function(t){var e=!1;return null!=t&&(e=this.x==t.x&&this.y==t.y||isNaN(this.x)&&isNaN(this.y)&&isNaN(t.x)&&isNaN(t.y)),e}},{key:"distanceTo",value:function(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}},{key:"add",value:function(e,r){if(null==e||null==r)throw new TypeError("Pixel.add cannot receive null values");return new t(this.x+e,this.y+r)}},{key:"offset",value:function(t){var e=this.clone();return t&&(e=this.add(t.x,t.y)),e}},{key:"destroy",value:function(){this.x=null,this.y=null,this.mode=null}}])&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();a.Mode={LeftTop:"lefttop",RightTop:"righttop",RightBottom:"rightbottom",LeftBottom:"leftbottom"};var s={startsWith:function(t,e){return 0==t.indexOf(e)},contains:function(t,e){return-1!=t.indexOf(e)},trim:function(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")},camelize:function(t){for(var e=t.split("-"),r=e[0],n=1,o=e.length;n<o;n++){var i=e[n];r+=i.charAt(0).toUpperCase()+i.substring(1)}return r},format:function(t,e,r){e||(e=window);return t.replace(s.tokenRegEx,function(t,n){for(var o,i=n.split(/\.+/),a=0;a<i.length;a++)0==a&&(o=e),o=o[i[a]];return"function"==typeof o&&(o=r?o.apply(null,r):o()),void 0===o?"undefined":o})},tokenRegEx:/\$\{([\w.]+?)\}/g,numberRegEx:/^([+-]?)(?=\d|\.\d)\d*(\.\d*)?([Ee]([+-]?\d+))?$/,isNumeric:function(t){return s.numberRegEx.test(t)},numericIf:function(t){return s.isNumeric(t)?parseFloat(t):t}},u={decimalSeparator:".",thousandsSeparator:",",limitSigDigs:function(t,e){var r=0;return e>0&&(r=parseFloat(t.toPrecision(e))),r},format:function(t,e,r,n){e=void 0!==e?e:0,r=void 0!==r?r:u.thousandsSeparator,n=void 0!==n?n:u.decimalSeparator,null!=e&&(t=parseFloat(t.toFixed(e)));var o=t.toString().split(".");1===o.length&&null==e&&(e=0);var i,a=o[0];if(r)for(var s=/(-?[0-9]+)([0-9]{3})/;s.test(a);)a=a.replace(s,"$1"+r+"$2");if(0==e)i=a;else{var c=o.length>1?o[1]:"0";null!=e&&(c+=new Array(e-c.length+1).join("0")),i=a+n+c}return i}};Number.prototype.limitSigDigs||(Number.prototype.limitSigDigs=function(t){return u.limitSigDigs(this,t)});var c=function(t,e){var r=Array.prototype.slice.apply(arguments,[2]);return function(){var n=r.concat(Array.prototype.slice.apply(arguments,[0]));return t.apply(e,n)}},l=function(t,e){return function(r){return t.call(e,r||window.event)}};function f(t){"@babel/helpers - typeof";return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h(n.key),n)}}function h(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var y=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.CLASS_NAME="SuperMap.Geometry",this.id=j.createUniqueID(this.CLASS_NAME+"_"),this.parent=null,this.bounds=null,this.SRID=null}return e=t,(r=[{key:"destroy",value:function(){this.id=null,this.bounds=null,this.SRID=null}},{key:"clone",value:function(){return new t}},{key:"setBounds",value:function(t){t&&(this.bounds=t.clone())}},{key:"clearBounds",value:function(){this.bounds=null,this.parent&&this.parent.clearBounds()}},{key:"extendBounds",value:function(t){this.getBounds()?this.bounds.extend(t):this.setBounds(t)}},{key:"getBounds",value:function(){return null==this.bounds&&this.calculateBounds(),this.bounds}},{key:"calculateBounds",value:function(){}},{key:"getVertices",value:function(t){}},{key:"getArea",value:function(){return 0}}])&&p(e.prototype,r),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}(),d=r(9216),m=r.n(d);function v(t){"@babel/helpers - typeof";return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var b,g,S,w,O,_,P,E=(g="",S="",w="pc",(O=navigator.userAgent.toLowerCase()).indexOf("msie")>-1||O.indexOf("trident")>-1&&O.indexOf("rv")>-1?(g="msie",b=O.match(/msie ([\d.]+)/)||O.match(/rv:([\d.]+)/)):O.indexOf("chrome")>-1?(g="chrome",b=O.match(/chrome\/([\d.]+)/)):O.indexOf("firefox")>-1?(g="firefox",b=O.match(/firefox\/([\d.]+)/)):O.indexOf("opera")>-1?(g="opera",b=O.match(/version\/([\d.]+)/)):O.indexOf("safari")>-1&&(g="safari",b=O.match(/version\/([\d.]+)/)),S=b?b[1]:"",O.indexOf("ipad")>-1||O.indexOf("ipod")>-1||O.indexOf("iphone")>-1?w="apple":O.indexOf("android")>-1&&(S=(b=O.match(/version\/([\d.]+)/))?b[1]:"",w="android"),{name:g,version:S,device:w}),k=(_=!0,P=E,document.createElement("canvas").getContext?("firefox"===P.name&&parseFloat(P.version)<5&&(_=!1),"safari"===P.name&&parseFloat(P.version)<4&&(_=!1),"opera"===P.name&&parseFloat(P.version)<10&&(_=!1),"msie"===P.name&&parseFloat(P.version)<9&&(_=!1)):_=!1,_),j=(function(){var t=navigator.userAgent.toLowerCase();-1===t.indexOf("webkit")&&t.indexOf("gecko")}(),{assign:function(t){for(var e=0;e<Object.getOwnPropertyNames(arguments).length;e++){var r=Object.getOwnPropertyNames(arguments)[e];if("caller"!=r&&"callee"!=r&&"length"!=r&&"arguments"!=r){var n=arguments[r];if(n)for(var o=0;o<Object.getOwnPropertyNames(n).length;o++){var i=Object.getOwnPropertyNames(n)[o];"caller"!=r&&"callee"!=r&&"length"!=r&&"arguments"!=r&&(t[i]=n[i])}}}return t},extend:function(t,e){if(t=t||{},e){for(var r in e){var n=e[r];void 0!==n&&(t[r]=n)}!("function"==typeof window.Event&&e instanceof window.Event)&&e.hasOwnProperty&&e.hasOwnProperty("toString")&&(t.toString=e.toString)}return t},copy:function(t,e){var r;if(t=t||{},e)for(var n in t)void 0!==(r=e[n])&&(t[n]=r)},reset:function(t){for(var e in t=t||{})if(t.hasOwnProperty(e)){if("object"===v(t[e])&&t[e]instanceof Array){for(var r in t[e])t[e][r].destroy&&t[e][r].destroy();t[e].length=0}else"object"===v(t[e])&&t[e]instanceof Object&&t[e].destroy&&t[e].destroy();t[e]=null}},getElement:function(){for(var t=[],e=0,r=arguments.length;e<r;e++){var n=arguments[e];if("string"==typeof n&&(n=document.getElementById(n)),1===arguments.length)return n;t.push(n)}return t},isElement:function(t){return!(!t||1!==t.nodeType)},isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},removeItem:function(t,e){for(var r=t.length-1;r>=0;r--)t[r]===e&&t.splice(r,1);return t},indexOf:function(t,e){if(null==t)return-1;if("function"==typeof t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1},modifyDOMElement:function(t,e,r,n,o,i,a,s){e&&(t.id=e),r&&(t.style.left=r.x+"px",t.style.top=r.y+"px"),n&&(t.style.width=n.w+"px",t.style.height=n.h+"px"),o&&(t.style.position=o),i&&(t.style.border=i),a&&(t.style.overflow=a),parseFloat(s)>=0&&parseFloat(s)<1?(t.style.filter="alpha(opacity="+100*s+")",t.style.opacity=s):1===parseFloat(s)&&(t.style.filter="",t.style.opacity="")},applyDefaults:function(t,e){t=t||{};var r="function"==typeof window.Event&&e instanceof window.Event;for(var n in e)(void 0===t[n]||!r&&e.hasOwnProperty&&e.hasOwnProperty(n)&&!t.hasOwnProperty(n))&&(t[n]=e[n]);return!r&&e&&e.hasOwnProperty&&e.hasOwnProperty("toString")&&!t.hasOwnProperty("toString")&&(t.toString=e.toString),t},getParameterString:function(t){var e=[];for(var r in t){var n,o=t[r];if(null!=o&&"function"!=typeof o)n=Array.isArray(o)||"[object Object]"===o.toString()?encodeURIComponent(JSON.stringify(o)):encodeURIComponent(o),e.push(encodeURIComponent(r)+"="+n)}return e.join("&")},urlAppend:function(t,e){var r=t;if(e){0===e.indexOf("?")&&(e=e.substring(1));var n=(t+" ").split(/[?&]/);r+=" "===n.pop()?e:n.length?"&"+e:"?"+e}return r},urlPathAppend:function(t,e){var r=t;if(!e)return r;0===e.indexOf("/")&&(e=e.substring(1));var n=t.split("?");return n[0].indexOf("/",n[0].length-1)<0&&(n[0]+="/"),r="".concat(n[0]).concat(e).concat(n.length>1?"?".concat(n[1]):"")},DEFAULT_PRECISION:14,toFloat:function(t,e){return null==e&&(e=j.DEFAULT_PRECISION),"number"!=typeof t&&(t=parseFloat(t)),0===e?t:parseFloat(t.toPrecision(e))},rad:function(t){return t*Math.PI/180},getParameters:function(t){t=null===t||void 0===t?window.location.href:t;var e="";if(s.contains(t,"?")){var r=t.indexOf("?")+1,n=s.contains(t,"#")?t.indexOf("#"):t.length;e=t.substring(r,n)}for(var o={},i=e.split(/[&;]/),a=0,u=i.length;a<u;++a){var c=i[a].split("=");if(c[0]){var l=c[0];try{l=decodeURIComponent(l)}catch(t){l=unescape(l)}var f=(c[1]||"").replace(/\+/g," ");try{f=decodeURIComponent(f)}catch(t){f=unescape(f)}1==(f=f.split(",")).length&&(f=f[0]),o[l]=f}}return o},lastSeqID:0,createUniqueID:function(t){return null==t&&(t="id_"),j.lastSeqID+=1,t+j.lastSeqID},normalizeScale:function(t){return t>1?1/t:t},getResolutionFromScale:function(t,e){var r;t&&(null==e&&(e="degrees"),r=1/(j.normalizeScale(t)*x[e]*96));return r},getScaleFromResolution:function(t,e){return null==e&&(e="degrees"),t*x[e]*96},getBrowser:function(){return E},isSupportCanvas:k,supportCanvas:function(){return j.isSupportCanvas},isInTheSameDomain:function(t){return!t||(-1===t.indexOf("//")||j.isSameDomain(t,document.location.toString()))},isSameDomain:function(t,e){return new(m())(t).normalize().origin()===new(m())(e).normalize().origin()},calculateDpi:function(t,e,r,n,o){if(t&&e&&r){var i,a=t.getWidth(),s=t.getHeight(),u=e.w,c=e.h;if(o=o||6378137,"degree"===(n=n||"degrees").toLowerCase()||"degrees"===n.toLowerCase()||"dd"===n.toLowerCase()){var l=a/u,f=s/c;i=254/(l>f?l:f)/r/(2*Math.PI*o/360)/1e4}else{i=254/(a/u)/r/1e4}return i}},toJSON:function(t){var e=t;if(null==e)return null;switch(e.constructor){case String:return e=(e=(e=(e=(e=(e=(e='"'+e.replace(/(["\\])/g,"\\$1")+'"').replace(/\n/g,"\\n")).replace(/\r/g,"\\r")).replace("<","&lt;")).replace(">","&gt;")).replace(/%/g,"%25")).replace(/&/g,"%26");case Array:for(var r="",n=0,o=e.length;n<o;n++)r+=j.toJSON(e[n]),n!==e.length-1&&(r+=",");return"["+r+"]";case Number:return isFinite(e)?String(e):null;case Boolean:return String(e);case Date:return"{'__type':\"System.DateTime\",'Year':"+e.getFullYear()+",'Month':"+(e.getMonth()+1)+",'Day':"+e.getDate()+",'Hour':"+e.getHours()+",'Minute':"+e.getMinutes()+",'Second':"+e.getSeconds()+",'Millisecond':"+e.getMilliseconds()+",'TimezoneOffset':"+e.getTimezoneOffset()+"}";default:if(null!=e.toJSON&&"function"==typeof e.toJSON)return e.toJSON();if("object"===v(e)){if(e.length){for(var i=[],a=0,s=e.length;a<s;a++)i.push(j.toJSON(e[a]));return"["+i.join(",")+"]"}var u=[];for(var c in e)"function"!=typeof e[c]&&"CLASS_NAME"!==c&&"parent"!==c&&u.push("'"+c+"':"+j.toJSON(e[c]));return u.length>0?"{"+u.join(",")+"}":"{}"}return e.toString()}},getResolutionFromScaleDpi:function(t,e,r,n){return n=n||6378137,r=r||"",t>0&&e>0?(t=j.normalizeScale(t),"degree"===r.toLowerCase()||"degrees"===r.toLowerCase()||"dd"===r.toLowerCase()?254/e/t/(2*Math.PI*n/360)/1e4:254/e/t/1e4):-1},getScaleFromResolutionDpi:function(t,e,r,n){return n=n||6378137,r=r||"",t>0&&e>0?"degree"===r.toLowerCase()||"degrees"===r.toLowerCase()||"dd"===r.toLowerCase()?254/e/t/(2*Math.PI*n/360)/1e4:254/e/t/1e4:-1},transformResult:function(t){return t.responseText&&"string"==typeof t.responseText&&(t=JSON.parse(t.responseText)),t},copyAttributes:function(t,e){if(t=t||{},e)for(var r in e){var n=e[r];void 0!==n&&"CLASS_NAME"!==r&&"function"!=typeof n&&(t[r]=n)}return t},copyAttributesWithClip:function(t,e,r){if(t=t||{},e)for(var n in e){var o=!1;if(r&&r.length)for(var i=0,a=r.length;i<a;i++)if(n===r[i]){o=!0;break}if(!0!==o){var s=e[n];void 0!==s&&"CLASS_NAME"!==n&&"function"!=typeof s&&(t[n]=s)}}return t},cloneObject:function(t){if(null===t||"object"!==v(t))return t;if(t instanceof Date){var e=new Date;return e.setTime(t.getTime()),e}if(t instanceof Array)return t.slice(0);if(t instanceof Object){var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]=j.cloneObject(t[n]));return r}throw new Error("Unable to copy obj! Its type isn't supported.")},lineIntersection:function(t,e,r,n){var o,i,a=null,s=(n.x-r.x)*(t.y-r.y)-(n.y-r.y)*(t.x-r.x),u=(e.x-t.x)*(t.y-r.y)-(e.y-t.y)*(t.x-r.x),c=(n.y-r.y)*(e.x-t.x)-(n.x-r.x)*(e.y-t.y);if(0!=c)i=u/c,a=(o=s/c)>=0&&i<=1&&o<=1&&i>=0?new y.Point(t.x+o*(e.x-t.x),t.y+o*(e.y-t.y)):"No Intersection";else if(0==s&&0==u){var l=Math.max(t.y,e.y),f=Math.min(t.y,e.y),p=Math.max(t.x,e.x),h=Math.min(t.x,e.x);a=(r.y>=f&&r.y<=l||n.y>=f&&n.y<=l)&&r.x>=h&&r.x<=p||n.x>=h&&n.x<=p?"Coincident":"Parallel"}else a="Parallel";return a},getTextBounds:function(t,e,r){document.body.appendChild(r),r.style.width="auto",r.style.height="auto",t.fontSize&&(r.style.fontSize=t.fontSize),t.fontFamily&&(r.style.fontFamily=t.fontFamily),t.fontWeight&&(r.style.fontWeight=t.fontWeight),r.style.position="relative",r.style.visibility="hidden",r.style.display="inline-block",r.innerHTML=e;var n=r.clientWidth,o=r.clientHeight;return document.body.removeChild(r),{textWidth:n,textHeight:o}},convertPath:function(t,e){return e?t.replace(/\{([\w-\.]+)\}/g,function(t,r){var n;return n=e.hasOwnProperty(r)?function(t){if(void 0==t||null==t)return"";if(t instanceof Date)return t.toJSON();if(function(t){if("string"!=typeof t&&"object"!==v(t))return!1;try{var e=t.toString();return"[object Object]"===e||"[object Array]"===e}catch(t){return!1}}(t))return JSON.stringify(t);return t.toString()}(e[r]):t,encodeURIComponent(n)}):t},hexToRgba:function(t,e){var r=[],n=[];if(3==(t=t.replace(/#/,"")).length){for(var o=[],i=0;i<3;i++)o.push(t.charAt(i)+t.charAt(i));t=o.join("")}for(var a=0;a<6;a+=2)r[a]="0x"+t.substr(a,2),n.push(parseInt(Number(r[a])));return n.push(e),"rgba("+n.join(",")+")"}}),x={inches:1,ft:12,mi:63360,m:39.3701,km:39370.1,dd:4374754,yd:36};x.in=x.inches,x.degrees=x.dd,x.nmi=1852*x.m;j.extend(x,{Inch:x.inches,Meter:39.37,Foot:12,IFoot:11.999976,ClarkeFoot:11.999868327581488,SearsFoot:11.999955194477684,GoldCoastFoot:11.999964589846002,IInch:.9999979999999999,MicroInch:999998e-9,Mil:9.99998e-7,Centimeter:.3937,Kilometer:39370,Yard:36,SearsYard:35.99986558343306,IndianYard:35.99987015540864,IndianYd37:35.999740205100004,IndianYd62:35.999880755999996,IndianYd75:35.999868945,IndianFoot:11.9999567087,IndianFt37:11.9999134017,IndianFt62:11.999960252000001,IndianFt75:11.999956315,Mile:63360,IYard:35.999928,IMile:63359.87328,NautM:72913.24,"Lat-66":4367838.370169282,"Lat-83":4367954.152606599,Decimeter:3.9370000000000003,Millimeter:.03937,Dekameter:393.7,Decameter:393.7,Hectometer:3937,GermanMeter:39.370535294205006,CaGrid:39.359685060000004,ClarkeChain:791.************,GunterChain:792.0000000000001,BenoitChain:791.9977268035781,SearsChain:791.9970428354235,ClarkeLink:7.91************,GunterLink:7.920000000000001,BenoitLink:7.919977268035781,SearsLink:7.919970428354236,Rod:198.00000000000014,IntnlChain:791.998416,IntnlLink:7.91998416,Perch:198.00000000000014,Pole:198.00000000000014,Furlong:7919.************,Rood:148.75036777426,CapeFoot:11.999868185255002,Brealey:14763.75,ModAmFt:12.000458400000001,Fathom:71.999856,"NautM-UK":72959.85408,"50kilometers":1968500,"150kilometers":5905500}),j.extend(x,{mm:x.Meter/1e3,cm:x.Meter/100,dm:100*x.Meter,km:1e3*x.Meter,kmi:x.nmi,fath:x.Fathom,ch:x.IntnlChain,link:x.IntnlLink,"us-in":x.inches,"us-ft":x.Foot,"us-yd":x.Yard,"us-ch":x.GunterChain,"us-mi":x.Mile,"ind-yd":x.IndianYd37,"ind-ft":x.IndianFt37,"ind-ch":791.9942845122}),x.degree=x.dd,x.meter=x.m,x.foot=x.ft,x.inch=x.inches,x.mile=x.mi,x.kilometer=x.km,x.yard=x.yd;var T={observers:!1,KEY_SPACE:32,KEY_BACKSPACE:8,KEY_TAB:9,KEY_RETURN:13,KEY_ESC:27,KEY_LEFT:37,KEY_UP:38,KEY_RIGHT:39,KEY_DOWN:40,KEY_DELETE:46,element:function(t){return t.target||t.srcElement},isSingleTouch:function(t){return t.touches&&1===t.touches.length},isMultiTouch:function(t){return t.touches&&t.touches.length>1},isLeftClick:function(t){return t.which&&1===t.which||t.button&&1===t.button},isRightClick:function(t){return t.which&&3===t.which||t.button&&2===t.button},stop:function(t,e){e||(t.preventDefault?t.preventDefault():t.returnValue=!1),t.stopPropagation?t.stopPropagation():t.cancelBubble=!0},findElement:function(t,e){for(var r=T.element(t);r.parentNode&&(!r.tagName||r.tagName.toUpperCase()!=e.toUpperCase());)r=r.parentNode;return r},observe:function(t,e,r,n){var o=j.getElement(t);if(n=n||!1,"keypress"===e&&(navigator.appVersion.match(/Konqueror|Safari|KHTML/)||o.attachEvent)&&(e="keydown"),this.observers||(this.observers={}),!o._eventCacheID){var i="eventCacheID_";o.id&&(i=o.id+"_"+i),o._eventCacheID=j.createUniqueID(i)}var a=o._eventCacheID;this.observers[a]||(this.observers[a]=[]),this.observers[a].push({element:o,name:e,observer:r,useCapture:n}),o.addEventListener?"mousewheel"===e?o.addEventListener(e,r,{useCapture:n,passive:!1}):o.addEventListener(e,r,n):o.attachEvent&&o.attachEvent("on"+e,r)},stopObservingElement:function(t){var e=j.getElement(t)._eventCacheID;this._removeElementObservers(T.observers[e])},_removeElementObservers:function(t){if(t)for(var e=t.length-1;e>=0;e--){var r=t[e],n=new Array(r.element,r.name,r.observer,r.useCapture);T.stopObserving.apply(this,n)}},stopObserving:function(t,e,r,n){n=n||!1;var o=j.getElement(t),i=o._eventCacheID;"keypress"===e&&(navigator.appVersion.match(/Konqueror|Safari|KHTML/)||o.detachEvent)&&(e="keydown");var a=!1,s=T.observers[i];if(s)for(var u=0;!a&&u<s.length;){var c=s[u];if(c.name===e&&c.observer===r&&c.useCapture===n){s.splice(u,1),0==s.length&&delete T.observers[i],a=!0;break}u++}return a&&(o.removeEventListener?o.removeEventListener(e,r,n):o&&o.detachEvent&&o.detachEvent("on"+e,r)),a},unloadCache:function(){if(T&&T.observers){for(var t in T.observers){var e=T.observers[t];T._removeElementObservers.apply(this,[e])}T.observers=!1}},CLASS_NAME:"SuperMap.Event"};function C(t){"@babel/helpers - typeof";return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,A(n.key),n)}}function A(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}T.observe(window,"resize",T.unloadCache,!1);var N=function(){function t(e,r,n,o,i){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.BROWSER_EVENTS=["mouseover","mouseout","mousedown","mouseup","mousemove","click","dblclick","rightclick","dblrightclick","resize","focus","blur","touchstart","touchmove","touchend","keydown","MSPointerDown","MSPointerUp","pointerdown","pointerup","MSGestureStart","MSGestureChange","MSGestureEnd","contextmenu"],this.listeners={},this.object=e,this.element=null,this.eventTypes=[],this.eventHandler=null,this.fallThrough=o,this.includeXY=!1,this.extensions={},this.extensionCount={},this.clearMouseListener=null,j.extend(this,i),null!=n)for(var a=0,s=n.length;a<s;a++)this.addEventType(n[a]);null!=r&&this.attachToElement(r),this.CLASS_NAME="SuperMap.Events"}return e=t,(r=[{key:"destroy",value:function(){for(var t in this.extensions)"boolean"!=typeof this.extensions[t]&&this.extensions[t].destroy();this.extensions=null,this.element&&(T.stopObservingElement(this.element),this.element.hasScrollEvent&&T.stopObserving(window,"scroll",this.clearMouseListener)),this.element=null,this.listeners=null,this.object=null,this.eventTypes=null,this.fallThrough=null,this.eventHandler=null}},{key:"addEventType",value:function(t){this.listeners[t]||(this.eventTypes.push(t),this.listeners[t]=[])}},{key:"attachToElement",value:function(t){this.element?T.stopObservingElement(this.element):(this.eventHandler=l(this.handleBrowserEvent,this),this.clearMouseListener=c(this.clearMouseCache,this)),this.element=t;for(var e=0,r=this.BROWSER_EVENTS.length;e<r;e++){var n=this.BROWSER_EVENTS[e];this.addEventType(n),T.observe(t,n,this.eventHandler)}T.observe(t,"dragstart",T.stop)}},{key:"on",value:function(t){for(var e in t)"scope"!==e&&t.hasOwnProperty(e)&&this.register(e,t.scope,t[e])}},{key:"register",value:function(e,r,n,o){if(e in t&&!this.extensions[e]&&(this.extensions[e]=new t[e](this)),null!=n&&-1!==j.indexOf(this.eventTypes,e)){null==r&&(r=this.object);var i=this.listeners[e];i||(i=[],this.listeners[e]=i,this.extensionCount[e]=0);var a={obj:r,func:n};o?(i.splice(this.extensionCount[e],0,a),"object"===C(o)&&o.extension&&this.extensionCount[e]++):i.push(a)}}},{key:"registerPriority",value:function(t,e,r){this.register(t,e,r,!0)}},{key:"un",value:function(t){for(var e in t)"scope"!==e&&t.hasOwnProperty(e)&&this.unregister(e,t.scope,t[e])}},{key:"unregister",value:function(t,e,r){null==e&&(e=this.object);var n=this.listeners[t];if(null!=n)for(var o=0,i=n.length;o<i;o++)if(n[o].obj===e&&n[o].func===r){n.splice(o,1);break}}},{key:"remove",value:function(t){null!=this.listeners[t]&&(this.listeners[t]=[])}},{key:"triggerEvent",value:function(t,e){var r=this.listeners[t];if(r&&0!=r.length){var n;null==e&&(e={}),e.object=this.object,e.element=this.element,e.type||(e.type=t);for(var o=0,i=(r=r.slice()).length;o<i;o++){var a=r[o];if(void 0!=(n=a.func.apply(a.obj,[e]))&&!1===n)break}return this.fallThrough||T.stop(e,!0),n}}},{key:"handleBrowserEvent",value:function(t){var e=t.type,r=this.listeners[e];if(r&&0!=r.length){var n=t.touches;if(n&&n[0]){for(var o,i=0,a=0,s=n.length,u=0;u<s;++u)i+=(o=n[u]).clientX,a+=o.clientY;t.clientX=i/s,t.clientY=a/s}this.includeXY&&(t.xy=this.getMousePosition(t)),this.triggerEvent(e,t)}}},{key:"clearMouseCache",value:function(){this.element.scrolls=null,this.element.lefttop=null;var t=document.body;t&&(0==t.scrollTop&&0==t.scrollLeft||!navigator.userAgent.match(/iPhone/i))&&(this.element.offsets=null)}},{key:"getMousePosition",value:function(t){if(this.includeXY?this.element.hasScrollEvent||(T.observe(window,"scroll",this.clearMouseListener),this.element.hasScrollEvent=!0):this.clearMouseCache(),!this.element.scrolls){var e=j.getViewportElement();this.element.scrolls=[e.scrollLeft,e.scrollTop]}return this.element.lefttop||(this.element.lefttop=[document.documentElement.clientLeft||0,document.documentElement.clientTop||0]),this.element.offsets||(this.element.offsets=j.pagePosition(this.element)),new a(t.clientX+this.element.scrolls[0]-this.element.offsets[0]-this.element.lefttop[0],t.clientY+this.element.scrolls[1]-this.element.offsets[1]-this.element.lefttop[1])}}])&&R(e.prototype,r),n&&R(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function I(t){return function(t){if(Array.isArray(t))return D(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return D(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?D(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function M(t){"@babel/helpers - typeof";return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,J(n.key),n)}}function J(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}N.prototype.BROWSER_EVENTS=["mouseover","mouseout","mousedown","mouseup","mousemove","click","dblclick","rightclick","dblrightclick","resize","focus","blur","touchstart","touchmove","touchend","keydown","MSPointerDown","MSPointerUp","pointerdown","pointerup","MSGestureStart","MSGestureChange","MSGestureEnd","contextmenu"];var F,U,B=function(){return t=function t(e,r,n){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!r||"function"!=typeof r&&"object"!==M(r)||"function"!=typeof r.Client)throw Error("Please enter the global variable of @elastic/elasticsearch@5.6.22 or elasticsearch@16.7.3 for the second parameter!");n=n||{},this.url=e;try{this.client=new r.Client({host:this.url})}catch(t){this.client=new r.Client({node:{url:new URL(this.url)}})}this.change=null,this.openGeoFence=!1,this.outOfGeoFence=null,this.geoFence=null,this.EVENT_TYPES=["change","error","outOfGeoFence"],this.events=new N(this,null,this.EVENT_TYPES),this.eventListeners=null,j.extend(this,n),this.eventListeners instanceof Object&&this.events.on(this.eventListeners)},(e=[{key:"setGeoFence",value:function(t){this.geoFence=t}},{key:"bulk",value:function(t,e){return this.client.bulk(t,this._handleCallback(e))}},{key:"clearScroll",value:function(t,e){return this.client.clearScroll(t,this._handleCallback(e))}},{key:"count",value:function(t,e){return this.client.count(t,this._handleCallback(e))}},{key:"create",value:function(t,e){return this.client.create(t,this._handleCallback(e))}},{key:"delete",value:function(t,e){return this.client.delete(t,this._handleCallback(e))}},{key:"deleteByQuery",value:function(t,e){return this.client.deleteByQuery(t,this._handleCallback(e))}},{key:"deleteScript",value:function(t,e){return this.client.deleteScript(t,this._handleCallback(e))}},{key:"deleteTemplate",value:function(t,e){return this.client.deleteTemplate(t,this._handleCallback(e))}},{key:"exists",value:function(t,e){return this.client.exists(t,this._handleCallback(e))}},{key:"existsSource",value:function(t,e){return this.client.existsSource(t,this._handleCallback(e))}},{key:"explain",value:function(t,e){return this.client.explain(t,this._handleCallback(e))}},{key:"fieldCaps",value:function(t,e){return this.client.fieldCaps(t,this._handleCallback(e))}},{key:"get",value:function(t,e){return this.client.get(t,this._handleCallback(e))}},{key:"getScript",value:function(t,e){return this.client.getScript(t,this._handleCallback(e))}},{key:"getSource",value:function(t,e){return this.client.getSource(t,this._handleCallback(e))}},{key:"getTemplate",value:function(t,e){return this.client.getTemplate(t,this._handleCallback(e))}},{key:"index",value:function(t,e){return this.client.index(t,this._handleCallback(e))}},{key:"info",value:function(t,e){return this.client.info(t,this._handleCallback(e))}},{key:"mget",value:function(t,e){return this.client.mget(t,this._handleCallback(e))}},{key:"msearch",value:function(t,e){var r=this;return r.client.msearch(t).then(function(t){return t=t.body||t,r._update(t.responses,e),t},function(t){return e(t),r.events.triggerEvent("error",{error:t}),t})}},{key:"msearchTemplate",value:function(t,e){return this.client.msearchTemplate(t,this._handleCallback(e))}},{key:"mtermvectors",value:function(t,e){return this.client.mtermvectors(t,this._handleCallback(e))}},{key:"ping",value:function(t,e){return this.client.ping(t,this._handleCallback(e))}},{key:"putScript",value:function(t,e){return this.client.putScript(t,this._handleCallback(e))}},{key:"putTemplate",value:function(t,e){return this.client.putTemplate(t,this._handleCallback(e))}},{key:"reindex",value:function(t,e){return this.client.reindex(t,this._handleCallback(e))}},{key:"reindexRessrottle",value:function(t,e){return this.client.reindexRessrottle(t,this._handleCallback(e))}},{key:"renderSearchTemplate",value:function(t,e){return this.client.renderSearchTemplate(t,this._handleCallback(e))}},{key:"scroll",value:function(t,e){return this.client.scroll(t,this._handleCallback(e))}},{key:"search",value:function(t,e){var r=this;return r.client.search(t).then(function(t){return t=t.body||t,r._update(t,e),t},function(t){return e&&e(t),r.events.triggerEvent("error",{error:t}),t})}},{key:"searchShards",value:function(t,e){return this.client.searchShards(t,this._handleCallback(e))}},{key:"searchTemplate",value:function(t,e){return this.client.searchTemplate(t,this._handleCallback(e))}},{key:"suggest",value:function(t,e){return this.client.suggest(t,this._handleCallback(e))}},{key:"termvectors",value:function(t,e){return this.client.termvectors(t,this._handleCallback(e))}},{key:"update",value:function(t,e){return this.client.update(t,this._handleCallback(e))}},{key:"updateByQuery",value:function(t,e){return this.client.updateByQuery(t,this._handleCallback(e))}},{key:"_handleCallback",value:function(t){return function(){var e=Array.from(arguments),r=e.shift(),n=e.shift(),o=n&&n.body;if(o){var i=n;e=[i.statusCode,i.headers],n=o}t.call.apply(t,[this,r,n].concat(I(e)))}}},{key:"_update",value:function(t,e){var r=this;t&&(r.data=t,r.openGeoFence&&r.geoFence&&r._validateDatas(t),r.events.triggerEvent("change",{data:r.data}),r.change?r.change&&r.change(t):e&&e(void 0,{responses:t}))}},{key:"_validateDatas",value:function(t){if(t){t instanceof Array||(t=[t]);var e,r=t.length;for(e=0;e<r;e++)this._validateData(t[e])}}},{key:"_validateData",value:function(t){var e=this;t.hits.hits.map(function(r){var n=r._source,o=e._getMeterPerMapUnit(e.geoFence.unit),i=e.geoFence.center[0]*o,a=e.geoFence.center[1]*o,s=n.x*o,u=n.y*o;return e._distance(s,u,i,a)>e.geoFence.radius&&(e.outOfGeoFence&&e.outOfGeoFence(t),e.events.triggerEvent("outOfGeoFence",{data:t})),r})}},{key:"_distance",value:function(t,e,r,n){return Math.sqrt((t-r)*(t-r)+(e-n)*(e-n))}},{key:"_getMeterPerMapUnit",value:function(t){var e;return"meter"===t?e=1:"degree"===t&&(e=2*Math.PI*6378137/360),e}}])&&L(t.prototype,e),r&&L(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}(),z=(r(3819),r(4537),r(9005)),Q=r.n(z),q=window.fetch,G={limitLength:1500,queryKeys:[],queryValues:[],supermap_callbacks:{},addQueryStrings:function(t){for(var e in t){this.queryKeys.push(e),"string"!=typeof t[e]&&(t[e]=j.toJSON(t[e]));var r=encodeURIComponent(t[e]);this.queryValues.push(r)}},issue:function(t){for(var e=this,r=e.getUid(),n=t.url,o=[],i=n,a=0,s=e.queryKeys?e.queryKeys.length:0,u=0;u<s;u++)if(i.length+e.queryKeys[u].length+2>=e.limitLength){if(0==a)return!1;o.push(i),i=n,a=0,u--}else if(i.length+e.queryKeys[u].length+2+e.queryValues[u].length>e.limitLength)for(var c=e.queryValues[u];c.length>0;){var l=e.limitLength-i.length-e.queryKeys[u].length-2;i.indexOf("?")>-1?i+="&":i+="?";var f=c.substring(0,l);"%"===f.substring(l-1,l)?(l-=1,f=c.substring(0,l)):"%"===f.substring(l-2,l-1)&&(l-=2,f=c.substring(0,l)),i+=e.queryKeys[u]+"="+f,c=c.substring(l),f.length>0&&(o.push(i),i=n,a=0)}else a++,i.indexOf("?")>-1?i+="&":i+="?",i+=e.queryKeys[u]+"="+e.queryValues[u];return o.push(i),e.send(o,"SuperMapJSONPCallbacks_"+r,t&&t.proxy)},getUid:function(){return 1e3*(new Date).getTime()+Math.floor(1e17*Math.random())},send:function(t,e,r){var n=t.length;if(n>0)return new Promise(function(o){for(var i=(new Date).getTime(),a=0;a<n;a++){var s=t[a];s.indexOf("?")>-1?s+="&":s+="?",s+="sectionCount="+n,s+="&sectionIndex="+a,s+="&jsonpUserID="+i,r&&(s=decodeURIComponent(s),s=r+encodeURIComponent(s)),Q()(s,{jsonpCallbackFunction:e,timeout:3e4}).then(function(t){o(t.json())})}})},GET:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings(t.params),this.issue(t)},POST:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)},PUT:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)},DELETE:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)}},V=function(){return U||45e3},H={commit:function(t,e,r,n){switch(t=t?t.toUpperCase():t){case"GET":return this.get(e,r,n);case"POST":return this.post(e,r,n);case"PUT":return this.put(e,r,n);case"DELETE":return this.delete(e,r,n);default:return this.get(e,r,n)}},supportDirectRequest:function(t,e){return!!j.isInTheSameDomain(t)||(void 0!=e.crossOrigin?e.crossOrigin:(void 0!=F?F:window.XMLHttpRequest&&"withCredentials"in new window.XMLHttpRequest)||e.proxy)},get:function(t,e,r){r=r||{};if(t=j.urlAppend(t,this._getParameterString(e||{})),t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){var n={url:t=t.replace(".json",".jsonp"),data:e};return G.GET(n)}return this.urlIsLong(t)?this._postSimulatie("GET",t.substring(0,t.indexOf("?")),e,r):this._fetch(t,e,r,"GET")},delete:function(t,e,r){r=r||{};if(t=j.urlAppend(t,this._getParameterString(e||{})),t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var n={url:t+="&_method=DELETE",data:e};return G.DELETE(n)}return this.urlIsLong(t)?this._postSimulatie("DELETE",t.substring(0,t.indexOf("?")),e,r):this._fetch(t,e,r,"DELETE")},post:function(t,e,r){if(r=r||{},t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var n={url:j.urlAppend(t,"_method=POST"),data:e};return G.POST(n)}return this._fetch(t,e,r,"POST")},put:function(t,e,r){if(r=r||{},t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var n={url:t+="&_method=PUT",data:e};return G.PUT(n)}return this._fetch(t,e,r,"PUT")},urlIsLong:function(t){for(var e=0,r=null,n=0,o=t.length;n<o;n++)(r=t.charCodeAt(n))<127?e++:128<=r&&r<=2047?e+=2:2048<=r&&r<=65535&&(e+=3);return!(e<2e3)},_postSimulatie:function(t,e,r,n){return e+=(e.indexOf("?")>-1?"&":"?")+"_method="+t,"string"!=typeof r&&(r=JSON.stringify(r)),this.post(e,r,n)},_processUrl:function(t,e){if(this._isMVTRequest(t))return t;if(-1===t.indexOf(".json")&&!e.withoutFormatSuffix)if(t.indexOf("?")<0)t+=".json";else{var r=t.split("?");2===r.length&&(t=r[0]+".json?"+r[1])}return e&&e.proxy&&("function"==typeof e.proxy?t=e.proxy(t):(t=decodeURIComponent(t),t=e.proxy+encodeURIComponent(t))),t},_fetch:function(t,e,r,n){return(r=r||{}).headers=r.headers||{},r.headers["Content-Type"]||FormData.prototype.isPrototypeOf(e)||(r.headers["Content-Type"]="application/x-www-form-urlencoded;charset=UTF-8"),r.timeout?this._timeout(r.timeout,q(t,{method:n,headers:r.headers,body:"PUT"===n||"POST"===n?e:void 0,credentials:this._getWithCredentials(r),mode:"cors",timeout:V()}).then(function(t){return t})):q(t,{method:n,body:"PUT"===n||"POST"===n?e:void 0,headers:r.headers,credentials:this._getWithCredentials(r),mode:"cors",timeout:V()}).then(function(t){return t})},_getWithCredentials:function(t){return!0===t.withCredentials?"include":!1===t.withCredentials?"omit":"same-origin"},_fetchJsonp:function(t,e){return e=e||{},Q()(t,{method:"GET",timeout:e.timeout}).then(function(t){return t})},_timeout:function(t,e){return new Promise(function(r,n){setTimeout(function(){n(new Error("timeout"))},t),e.then(r,n)})},_getParameterString:function(t){var e=[];for(var r in t){var n,o=t[r];if(null!=o&&"function"!=typeof o)n=Array.isArray(o)||"[object Object]"===o.toString()?encodeURIComponent(JSON.stringify(o)):encodeURIComponent(o),e.push(encodeURIComponent(r)+"="+n)}return e.join("&")},_isMVTRequest:function(t){return t.indexOf(".mvt")>-1||t.indexOf(".pbf")>-1}};function K(t){"@babel/helpers - typeof";return(K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,W(n.key),n)}}function W(t){var e=function(t,e){if("object"!=K(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=K(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==K(e)?e:e+""}var $=function(){return t=function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.value=e||"",this.name=r||"token",this.CLASS_NAME="SuperMap.Credential"},(e=[{key:"getUrlParameters",value:function(){return this.name+"="+this.value}},{key:"getValue",value:function(){return this.value}},{key:"destroy",value:function(){this.value=null,this.name=null}}])&&Y(t.prototype,e),r&&Y(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function X(t){"@babel/helpers - typeof";return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Z(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tt(n.key),n)}}function tt(t){var e=function(t,e){if("object"!=X(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=X(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==X(e)?e:e+""}$.CREDENTIAL=null;var et=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,n=[{key:"generateToken",value:function(t,e){var r=this.servers[t];if(r)return H.post(r.tokenServiceUrl,JSON.stringify(e.toJSON())).then(function(t){return t.text()})}},{key:"registerServers",value:function(t){this.servers=this.servers||{},j.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var r=t[e];this.servers[r.server]=r}}},{key:"registerToken",value:function(t,e){if(this.tokens=this.tokens||{},t&&e){var r=this._getTokenStorageKey(t);this.tokens[r]=e}}},{key:"registerKey",value:function(t,e){if(this.keys=this.keys||{},t&&!(t.length<1)&&e){t=j.isArray(t)?t:[t];for(var r=0;r<t.length;r++){var n=this._getUrlRestString(t[0])||t[0];this.keys[n]=e}}}},{key:"getServerInfo",value:function(t){return this.servers=this.servers||{},this.servers[t]}},{key:"getToken",value:function(t){if(t){this.tokens=this.tokens||{};var e=this._getTokenStorageKey(t);return this.tokens[e]}}},{key:"getKey",value:function(t){this.keys=this.keys||{};var e=this._getUrlRestString(t)||t;return this.keys[e]}},{key:"loginiServer",value:function(t,e,r,n){t=j.urlPathAppend(t,"services/security/login");var o={username:e&&e.toString(),password:r&&r.toString(),rememberme:n};o=JSON.stringify(o);return H.post(t,o,{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"}}).then(function(t){return t.json()})}},{key:"logoutiServer",value:function(t){t=j.urlPathAppend(t,"services/security/logout");return H.get(t,"",{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withoutFormatSuffix:!0}).then(function(){return!0}).catch(function(){return!1})}},{key:"loginOnline",value:function(e,r){var n=t.SSO+"/login?service="+e;this._open(n,r)}},{key:"loginiPortal",value:function(t,e,r){t=j.urlPathAppend(t,"web/login");var n={username:e&&e.toString(),password:r&&r.toString()};n=JSON.stringify(n);return H.post(t,n,{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withCredentials:!1}).then(function(t){return t.json()})}},{key:"logoutiPortal",value:function(t){t=j.urlPathAppend(t,"services/security/logout");return H.get(t,"",{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withCredentials:!0,withoutFormatSuffix:!0}).then(function(){return!0}).catch(function(){return!1})}},{key:"loginManager",value:function(t,e){var r=j.urlPathAppend(t,"/security/tokens"),n=e||{},o={username:n.userName&&n.userName.toString(),password:n.password&&n.password.toString()};o=JSON.stringify(o);var i=this;return H.post(r,o,{headers:{Accept:"*/*","Content-Type":"application/json; charset=UTF-8"}}).then(function(t){return t.text()}).then(function(t){return i.imanagerToken=t,t})}},{key:"destroyAllCredentials",value:function(){this.keys=null,this.tokens=null,this.servers=null}},{key:"destroyToken",value:function(t){if(t){var e=this._getTokenStorageKey(t);this.tokens=this.tokens||{},this.tokens[e]&&delete this.tokens[e]}}},{key:"destroyKey",value:function(t){if(t){this.keys=this.keys||{};var e=this._getUrlRestString(t)||t;this.keys[e]&&delete this.keys[e]}}},{key:"appendCredential",value:function(t){var e=t,r=this.getToken(t),n=r?new $(r,"token"):null;return n||(n=(r=this.getKey(t))?new $(r,"key"):null),n&&(e=j.urlAppend(e,n.getUrlParameters())),e}},{key:"_open",value:function(t,e){e=null==e||e;var r=window.screen.availWidth/2-this.INNER_WINDOW_WIDTH/2,n=window.screen.availHeight/2-this.INNER_WINDOW_HEIGHT/2,o="height="+this.INNER_WINDOW_HEIGHT+", width="+this.INNER_WINDOW_WIDTH+",top="+n+", left="+r+",toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no";e?window.open(t,"login"):window.open(t,"login",o)}},{key:"_getTokenStorageKey",value:function(t){var e=t.match(/(.*?):\/\/([^\/]+)/i);return e?e[0]:t}},{key:"_getUrlRestString",value:function(t){if(!t)return t;var e=t.match(/(http|https):\/\/(.*\/rest)/i);return e?e[0]:t}}],(r=null)&&Z(e.prototype,r),n&&Z(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();et.INNER_WINDOW_WIDTH=600,et.INNER_WINDOW_HEIGHT=600,et.SSO="https://sso.supermap.com",et.ONLINE="https://www.supermapol.com";var rt="GEOJSON",nt="ISERVER",ot="FGB",it={CONTAIN:"CONTAIN",CROSS:"CROSS",DISJOINT:"DISJOINT",IDENTITY:"IDENTITY",INTERSECT:"INTERSECT",NONE:"NONE",OVERLAP:"OVERLAP",TOUCH:"TOUCH",WITHIN:"WITHIN"},at="KILOMETER",st="INCH",ut="FOOT",ct={CUSTOMINTERVAL:"CUSTOMINTERVAL",EQUALINTERVAL:"EQUALINTERVAL",LOGARITHM:"LOGARITHM",QUANTILE:"QUANTILE",SQUAREROOT:"SQUAREROOT",STDDEVIATION:"STDDEVIATION"},lt={BLACK_WHITE:"BLACKWHITE",BLUE_BLACK:"BLUEBLACK",BLUE_RED:"BLUERED",BLUE_WHITE:"BLUEWHITE",CYAN_BLACK:"CYANBLACK",CYAN_BLUE:"CYANBLUE",CYAN_GREEN:"CYANGREEN",CYAN_WHITE:"CYANWHITE",GREEN_BLACK:"GREENBLACK",GREEN_BLUE:"GREENBLUE",GREEN_ORANGE_VIOLET:"GREENORANGEVIOLET",GREEN_RED:"GREENRED",GREEN_WHITE:"GREENWHITE",PINK_BLACK:"PINKBLACK",PINK_BLUE:"PINKBLUE",PINK_RED:"PINKRED",PINK_WHITE:"PINKWHITE",RAIN_BOW:"RAINBOW",RED_BLACK:"REDBLACK",RED_WHITE:"REDWHITE",SPECTRUM:"SPECTRUM",TERRAIN:"TERRAIN",YELLOW_BLACK:"YELLOWBLACK",YELLOW_BLUE:"YELLOWBLUE",YELLOW_GREEN:"YELLOWGREEN",YELLOW_RED:"YELLOWRED",YELLOW_WHITE:"YELLOWWHITE"},ft={INDEXEDHDFS:"INDEXEDHDFS",UDB:"UDB",MONGODB:"MONGODB",PG:"PG"},pt={CLIP:"clip",INTERSECT:"intersect"},ht={SQUAREMETER:"SquareMeter",SQUAREKILOMETER:"SquareKiloMeter",HECTARE:"Hectare",ARE:"Are",ACRE:"Acre",SQUAREFOOT:"SquareFoot",SQUAREYARD:"SquareYard",SQUAREMILE:"SquareMile"},yt={METER:"Meter",KILOMETER:"Kilometer",YARD:"Yard",FOOT:"Foot",MILE:"Mile"},dt={MAX:"max",MIN:"min",AVERAGE:"average",SUM:"sum",VARIANCE:"variance",STDDEVIATION:"stdDeviation"},mt={SUMMARYMESH:"SUMMARYMESH",SUMMARYREGION:"SUMMARYREGION"},vt={REGIONNOOVERLAP:"REGIONNOOVERLAP",REGIONNOOVERLAPWITH:"REGIONNOOVERLAPWITH",REGIONCONTAINEDBYREGION:"REGIONCONTAINEDBYREGION",REGIONCOVEREDBYREGION:"REGIONCOVEREDBYREGION",LINENOOVERLAP:"LINENOOVERLAP",LINENOOVERLAPWITH:"LINENOOVERLAPWITH",POINTNOIDENTICAL:"POINTNOIDENTICAL"};function bt(t){"@babel/helpers - typeof";return(bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function gt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,St(n.key),n)}}function St(t){var e=function(t,e){if("object"!=bt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=bt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==bt(e)?e:e+""}var wt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.alias=null,this.connect=null,this.dataBase=null,this.driver=null,this.engineType=null,this.exclusive=null,this.OpenLinkTable=null,this.password=null,this.readOnly=null,this.server=null,this.user=null,e&&j.extend(this,e),this.CLASS_NAME="SuperMap.DatasourceConnectionInfo"},(e=[{key:"destroy",value:function(){var t=this;t.alias=null,t.connect=null,t.dataBase=null,t.driver=null,t.engineType=null,t.exclusive=null,t.OpenLinkTable=null,t.password=null,t.readOnly=null,t.server=null,t.user=null}}])&&gt(t.prototype,e),r&&gt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Ot(t){"@babel/helpers - typeof";return(Ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Pt(n.key),n)}}function Pt(t){var e=function(t,e){if("object"!=Ot(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ot(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ot(e)?e:e+""}var Et=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.type=ft.UDB,this.datasetName="analystResult",this.datasourceInfo=null,this.outputPath="",j.extend(this,e),this.CLASS_NAME="SuperMap.OutputSetting"},(e=[{key:"destroy",value:function(){var t=this;t.type=null,t.datasetName=null,t.outputPath=null,t.datasourceInfo instanceof wt&&(t.datasourceInfo.destroy(),t.datasourceInfo=null)}}])&&_t(t.prototype,e),r&&_t(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function kt(t){"@babel/helpers - typeof";return(kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function jt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,xt(n.key),n)}}function xt(t){var e=function(t,e){if("object"!=kt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=kt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==kt(e)?e:e+""}var Tt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.items=null,this.numericPrecision=1,this.rangeMode=ct.EQUALINTERVAL,this.rangeCount="",this.colorGradientType=lt.YELLOW_RED,j.extend(this,e),this.CLASS_NAME="SuperMap.MappingParameters"},(e=[{key:"destroy",value:function(){var t=this;if(t.items){if(t.items.length>0)for(var e in t.items)t.items[e].destroy(),t.items[e]=null;t.items=null}t.numericPrecision=null,t.rangeMode=null,t.rangeCount=null,t.colorGradientType=null}}])&&jt(t.prototype,e),r&&jt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Ct(t){"@babel/helpers - typeof";return(Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Rt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,At(n.key),n)}}function At(t){var e=function(t,e){if("object"!=Ct(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ct(e)?e:e+""}var Nt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.query="",this.resolution=80,this.method=0,this.meshType=0,this.fields="",this.radius=300,this.meshSizeUnit=yt.METER,this.radiusUnit=yt.METER,this.areaUnit=ht.SQUAREMILE,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.KernelDensityJobParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.query=null,this.resolution=null,this.method=null,this.radius=null,this.meshType=null,this.fields=null,this.meshSizeUnit=null,this.radiusUnit=null,this.areaUnit=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&Rt(t.prototype,e),r&&Rt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function It(t){"@babel/helpers - typeof";return(It="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Dt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mt(n.key),n)}}function Mt(t){var e=function(t,e){if("object"!=It(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=It(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==It(e)?e:e+""}var Lt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.datasetQuery="",this.geometryQuery="",this.mode=it.CONTAIN,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.SingleObjectQueryJobsParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.datasetQuery=null,this.geometryQuery=null,this.mode=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&Dt(t.prototype,e),r&&Dt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Jt(t){"@babel/helpers - typeof";return(Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ft(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ut(n.key),n)}}function Ut(t){var e=function(t,e){if("object"!=Jt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Jt(e)?e:e+""}var Bt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.groupField="",this.attributeField="",this.statisticModes="",this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.SummaryAttributesJobsParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.groupField=null,this.attributeField=null,this.statisticModes=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&Ft(t.prototype,e),r&&Ft(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function zt(t){"@babel/helpers - typeof";return(zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Qt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,qt(n.key),n)}}function qt(t){var e=function(t,e){if("object"!=zt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=zt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==zt(e)?e:e+""}var Gt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.regionDataset="",this.query="",this.resolution=100,this.meshType=0,this.statisticModes=dt.AVERAGE,this.fields="",this.type=mt.SUMMARYMESH,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.SummaryMeshJobParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"type"!==r?"output"!==r?("SUMMARYMESH"===t.type&&"regionDataset"!==r||"SUMMARYREGION"===t.type&&!n(["meshType","resolution","query"],r))&&(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):e.type=t[r]:(e.input=e.input||{},e.input[r]=t[r]);function n(t,e){for(var r=t.length;r--;)if(t[r]===e)return!0;return!1}}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.query=null,this.resolution=null,this.statisticModes=null,this.meshType=null,this.fields=null,this.regionDataset=null,this.type=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&Qt(t.prototype,e),r&&Qt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Vt(t){"@babel/helpers - typeof";return(Vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ht(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Kt(n.key),n)}}function Kt(t){var e=function(t,e){if("object"!=Vt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Vt(e)?e:e+""}var Yt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.regionDataset="",this.sumShape=!0,this.query="",this.standardSummaryFields=!1,this.standardFields="",this.standardStatisticModes="",this.weightedSummaryFields=!1,this.weightedFields="",this.weightedStatisticModes="",this.meshType=0,this.resolution=100,this.meshSizeUnit=yt.METER,this.type=mt.SUMMARYMESH,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.SummaryRegionJobParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"type"!==r&&"type"!==r?"output"!==r?("SUMMARYREGION"===t.type||"SUMMARYMESH"===t.type&&"regionDataset"!==r)&&(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):e.type=t[r]:(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.sumShape=null,this.regionDataset=null,this.query=null,this.standardSummaryFields=null,this.standardFields=null,this.standardStatisticModes=null,this.weightedSummaryFields=null,this.weightedFields=null,this.weightedStatisticModes=null,this.meshType=null,this.resolution=null,this.meshSizeUnit=null,this.type=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&Ht(t.prototype,e),r&&Ht(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Wt(t){"@babel/helpers - typeof";return(Wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function $t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Xt(n.key),n)}}function Xt(t){var e=function(t,e){if("object"!=Wt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Wt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Wt(e)?e:e+""}var Zt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.datasetOverlay="",this.mode="",this.srcFields="",this.overlayFields="",this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.OverlayGeoJobParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!=r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.datasetOverlay=null,this.mode=null,this.srcFields=null,this.overlayFields=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&$t(t.prototype,e),r&&$t(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function te(t){"@babel/helpers - typeof";return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ee(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,re(n.key),n)}}function re(t){var e=function(t,e){if("object"!=te(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=te(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==te(e)?e:e+""}var ne=function(){return t=function t(e){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.datasetName="",this.bounds="",this.distance="",this.distanceField="",this.distanceUnit=yt.METER,this.dissolveField="",this.output=null,this.mappingParameters=null,!e)return this;j.extend(this,e),this.CLASS_NAME="SuperMap.BuffersAnalystJobsParameter"},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},"bounds"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.bounds=null,this.distance=null,this.distanceField=null,this.distanceUnit=null,this.dissolveField=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&ee(t.prototype,e),r&&ee(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function oe(t){"@babel/helpers - typeof";return(oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ie(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ae(n.key),n)}}function ae(t){var e=function(t,e){if("object"!=oe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=oe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==oe(e)?e:e+""}var se=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasetName="",this.datasetTopology="",this.tolerance="",this.rule=vt.REGIONNOOVERLAP,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.TopologyValidatorJobsParameter")},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.datasetTopology=null,this.tolerance=null,this.rule=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&ie(t.prototype,e),r&&ie(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function ue(t){"@babel/helpers - typeof";return(ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ce(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,le(n.key),n)}}function le(t){var e=function(t,e){if("object"!=ue(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ue(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ue(e)?e:e+""}var fe=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e.filters&&"string"==typeof e.filters&&(e.filters=e.filters.split(",")),this.address=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,j.extend(this,e)},(e=[{key:"destroy",value:function(){this.address=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null}}])&&ce(t.prototype,e),r&&ce(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function pe(t){"@babel/helpers - typeof";return(pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function he(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ye(n.key),n)}}function ye(t){var e=function(t,e){if("object"!=pe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pe(e)?e:e+""}var de=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e.filters&&(e.filters=e.filters.split(",")),this.x=null,this.y=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,this.geoDecodingRadius=null,j.extend(this,e)},(e=[{key:"destroy",value:function(){this.x=null,this.y=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,this.geoDecodingRadius=null}}])&&he(t.prototype,e),r&&he(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function me(t){"@babel/helpers - typeof";return(me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ve(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,be(n.key),n)}}function be(t){var e=function(t,e){if("object"!=me(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=me(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==me(e)?e:e+""}var ge=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=e||{},this.datasetName="",this.datasetVectorClip="",this.geometryClip="",this.mode=pt.CLIP,this.output=null,this.mappingParameters=null,j.extend(this,e),this.CLASS_NAME="SuperMap.VectorClipJobsParameter"},r=[{key:"toObject",value:function(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}],(e=[{key:"destroy",value:function(){this.datasetName=null,this.datasetVectorClip=null,this.geometryClip=null,this.mode=null,this.output instanceof Et&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof Tt&&(this.mappingParameters.destroy(),this.mappingParameters=null)}}])&&ve(t.prototype,e),r&&ve(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}(),Se=window.SuperMap=window.SuperMap||{};Se.REST=Se.REST||{};var we=function(){try{return mapv}catch(t){return{}}}(),Oe=function(t){var e;if(!t)return e;return["m","meter","meters"].indexOf(t.toLocaleLowerCase())>-1?e=1:["degrees","deg","degree","dd"].indexOf(t.toLocaleLowerCase())>-1?e=2*Math.PI*6378137/360:t===at?e=1e3:t===st?e=.025399999918:t===ut&&(e=.3048),e};function _e(t){"@babel/helpers - typeof";return(_e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Pe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ee(n.key),n)}}function Ee(t){var e=function(t,e){if("object"!=_e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_e(e)?e:e+""}function ke(t,e,r){return e=Ce(e),je(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Ce(t).constructor):e.apply(t,r))}function je(t,e){if(e&&("object"==_e(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function xe(t,e,r,n){var o=Te(Ce(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Te(){return(Te="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Ce(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Ce(t){return(Ce=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Re(t,e){return(Re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Ae=we.baiduMapLayer?we.baiduMapLayer.__proto__:Function,Ne=function(t){function e(t,r,n,o){var i;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),i=ke(this,e,[t,n,o]),!Ae)return je(i,i);var a=i;return o=o||{},a.init(o),a.argCheck(o),i.canvasLayer=r,i.clickEvent=i.clickEvent.bind(i),i.mousemoveEvent=i.mousemoveEvent.bind(i),i.bindEvent(),i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Re(t,e)}(e,Ae),r=e,(n=[{key:"clickEvent",value:function(t){var r=t.xy,n=this.devicePixelRatio||1;xe(e,"clickEvent",this,3)([{x:r.x/n,y:r.y/n},t])}},{key:"mousemoveEvent",value:function(t){var r=t.xy;xe(e,"mousemoveEvent",this,3)([r,t])}},{key:"bindEvent",value:function(){var t=this.map;this.options.methods&&(this.options.methods.click&&t.events.on({click:this.clickEvent}),this.options.methods.mousemove&&t.events.on({mousemove:this.mousemoveEvent}))}},{key:"unbindEvent",value:function(){var t=this.map;this.options.methods&&(this.options.methods.click&&t.events.un({click:this.clickEvent}),this.options.methods.mousemove&&t.events.un({mousemove:this.mousemoveEvent}))}},{key:"getContext",value:function(){return this.canvasLayer&&this.canvasLayer.canvasContext}},{key:"addData",value:function(t,e){var r=t;t&&t.get&&(r=t.get()),this.dataSet.add(r),this.update({options:e})}},{key:"setData",value:function(t,e){var r=t;t&&t.get&&(r=t.get()),this.dataSet=this.dataSet||new we.DataSet,this.dataSet.set(r),this.update({options:e})}},{key:"getData",value:function(){return this.dataSet}},{key:"removeData",value:function(t){if(this.dataSet){var e=this.dataSet.get({filter:function(e){return null==t||"function"!=typeof t||!t(e)}});this.dataSet.set(e),this.update({options:null})}}},{key:"clearData",value:function(){this.dataSet&&this.dataSet.clear(),this.update({options:null})}},{key:"render",value:function(t){this._canvasUpdate(t)}},{key:"transferToMercator",value:function(){if(this.options.coordType&&["bd09mc","coordinates_mercator"].indexOf(this.options.coordType)>-1){var t=this.dataSet.get();t=this.dataSet.transferCoordinate(t,function(t){var e=Se.Projection.transform({x:t[0],y:t[1]},"EPSG:3857","EPSG:4326");return[e.x,e.y]},"coordinates","coordinates"),this.dataSet._set(t)}}},{key:"_canvasUpdate",value:function(t){if(this.canvasLayer){var e=this.options.animation,r=this.getContext(),n=this.map;if(this.isEnabledTime()){if(void 0===t)return void this.clear(r);"2d"===this.context&&(r.save(),r.globalCompositeOperation="destination-out",r.fillStyle="rgba(0, 0, 0, .1)",r.fillRect(0,0,r.canvas.width,r.canvas.height),r.restore())}else this.clear(r);if("2d"===this.context)for(var o in this.options)r[o]=this.options[o];else r.clear(r.COLOR_BUFFER_BIT);if(!(this.options.minZoom&&n.getZoom()<this.options.minZoom||this.options.maxZoom&&n.getZoom()>this.options.maxZoom)){var i=this.canvasLayer,a={fromColumn:"coordinates",transferCoordinate:function(t){var e={lon:t[0],lat:t[1]},r=n.getViewPortPxFromLonLat(e);return[r.x,r.y]}};void 0!==t&&(a.filter=function(r){var n=e.trails||10;return t&&r.time>t-n&&r.time<t});var s=this.dataSet.get(a);this.processData(s);var u=n.getResolution()*Oe("DEGREE");"m"===this.options.unit?(this.options.size&&(this.options._size=this.options.size/u),this.options.width&&(this.options._width=this.options.width/u),this.options.height&&(this.options._height=this.options.height/u)):(this.options._size=this.options.size,this.options._height=this.options.height,this.options._width=this.options.width);var c=n.getViewPortPxFromLonLat(i.transferToMapLatLng({lon:0,lat:0}));this.drawContext(r,s,this.options,c),this.options.updateCallback&&this.options.updateCallback(t)}}}},{key:"init",value:function(t){this.options=t,this.initDataRange(t),this.context=this.options.context||"2d",this.options.zIndex&&this.canvasLayer&&this.canvasLayer.setZIndex(this.options.zIndex),this.initAnimator()}},{key:"addAnimatorEvent",value:function(){this.map.events.on({movestart:this.animatorMovestartEvent.bind(this)}),this.map.events.on({moveend:this.animatorMoveendEvent.bind(this)})}},{key:"clear",value:function(t){t&&t.clearRect&&t.clearRect(0,0,t.canvas.width,t.canvas.height)}},{key:"show",value:function(){this.map.addLayer(this.canvasLayer)}},{key:"hide",value:function(){this.map.removeLayer(this.canvasLayer)}},{key:"draw",value:function(){this.canvasLayer.redraw()}}])&&Pe(r.prototype,n),o&&Pe(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Ie(t){"@babel/helpers - typeof";return(Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function De(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Me(n.key),n)}}function Me(t){var e=function(t,e){if("object"!=Ie(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ie(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ie(e)?e:e+""}function Le(t,e,r){return e=Be(e),Je(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Be(t).constructor):e.apply(t,r))}function Je(t,e){if(e&&("object"==Ie(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Fe(t,e,r,n){var o=Ue(Be(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Ue(){return(Ue="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Be(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Be(t){return(Be=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ze(t,e){return(ze=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Qe=function(t){function e(t,r){var n;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Le(this,e,[t,r])).dataSet=null,n.options=null,n.supported=!1,n.canvas=null,n.canvasContext=null,r&&Se.Util.extend(n,r),n.canvas=document.createElement("canvas"),!n.canvas.getContext)return Je(n);n.supported=!0,n.canvas.style.position="absolute",n.canvas.style.top="0px",n.canvas.style.left="0px",n.div.appendChild(n.canvas);var o=n.options&&n.options.context||"2d";n.canvasContext=n.canvas.getContext(o);var i="undefined"==typeof window?{}:window,a=n.devicePixelRatio=i.devicePixelRatio||1;return"2d"===o&&n.canvasContext.scale(a,a),n.attribution="© 2018 百度 <a href='https://mapv.baidu.com' target='_blank'>MapV</a> with <span>© <a target='_blank' href='https://iclient.supermap.io' style='color: #08c;text-decoration: none;'>SuperMap iClient</a></span>",n.CLASS_NAME="SuperMap.Layer.MapVLayer",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ze(t,e)}(e,Se.Layer),r=e,(n=[{key:"destroy",value:function(){this.renderer&&this.renderer.animator&&(this.renderer.animator.stop(),this.renderer.animator=null),this.dataSet=null,this.options=null,this.renderer=null,this.supported=null,this.canvas=null,this.canvasContext=null,this.maxWidth=null,this.maxHeight=null,Fe(e,"destroy",this,3)([])}},{key:"addData",value:function(t,e){this.renderer&&this.renderer.addData(t,e)}},{key:"setData",value:function(t,e){this.renderer&&this.renderer.setData(t,e)}},{key:"getData",value:function(){return this.renderer&&(this.dataSet=this.renderer.getData()),this.dataSet}},{key:"removeData",value:function(t){this.renderer&&this.renderer.removeData(t)}},{key:"clearData",value:function(){this.renderer.clearData()}},{key:"setMap",value:function(t){Fe(e,"setMap",this,3)([t]),this.renderer=new Ne(t,this,this.dataSet,this.options),this.renderer.devicePixelRatio=this.devicePixelRatio,this.supported?this.redraw():this.map.removeLayer(this)}},{key:"moveTo",value:function(t,r,n){if(Fe(e,"moveTo",this,3)([t,r,n]),this.supported){if(this.zoomChanged=r,!n){this.div.style.visibility="hidden",this.div.style.left=-parseInt(this.map.layerContainerDiv.style.left)+"px",this.div.style.top=-parseInt(this.map.layerContainerDiv.style.top)+"px";var o=this.map.getSize();this.div.style.width=parseInt(o.w)+"px",this.div.style.height=parseInt(o.h)+"px","heatmap"===this.options.draw?(this.canvas.width=parseInt(o.w)*this.devicePixelRatio,this.canvas.height=parseInt(o.h)*this.devicePixelRatio):(this.canvas.width=parseInt(o.w),this.canvas.height=parseInt(o.h)),this.canvas.style.width=this.div.style.width,this.canvas.style.height=this.div.style.height,this.maxWidth=o.w,this.maxHeight=o.h,this.div.style.visibility="",r||this.renderer&&this.renderer.render()}r&&this.renderer&&this.renderer.render()}}},{key:"transferToMapLatLng",value:function(t){var e="EPSG:4326",r=this.map.getUnits()||"degree";return["m","meter"].indexOf(r.toLowerCase())>-1&&(e="EPSG:3857"),new Se.LonLat(t.lon,t.lat).transform("EPSG:4326",e)}}])&&De(r.prototype,n),o&&De(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function qe(t){"@babel/helpers - typeof";return(qe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ge(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ve(n.key),n)}}function Ve(t){var e=function(t,e){if("object"!=qe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=qe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==qe(e)?e:e+""}Se.Layer.MapVLayer=Qe;var He=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.data=null,this.keepData=!1,j.extend(this,e),this.options=e,this.CLASS_NAME="SuperMap.Format"},(e=[{key:"destroy",value:function(){}},{key:"read",value:function(t){}},{key:"write",value:function(t){}}])&&Ge(t.prototype,e),r&&Ge(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Ke(t){"@babel/helpers - typeof";return(Ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ye(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,We(n.key),n)}}function We(t){var e=function(t,e){if("object"!=Ke(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ke(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ke(e)?e:e+""}function $e(t,e,r){return e=Xe(e),function(t,e){if(e&&("object"==Ke(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Xe(t).constructor):e.apply(t,r))}function Xe(t){return(Xe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ze(t,e){return(Ze=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var tr=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=$e(this,e,[t])).indent="    ",r.space=" ",r.newline="\n",r.level=0,r.pretty=!1,r.nativeJSON=!(!window.JSON||"function"!=typeof JSON.parse||"function"!=typeof JSON.stringify),r.CLASS_NAME="SuperMap.Format.JSON",r.serialize={object:function(t){if(null==t)return"null";if(t.constructor===Date)return this.serialize.date.apply(this,[t]);if(t.constructor===Array)return this.serialize.array.apply(this,[t]);var e,r,n,o=["{"];this.level+=1;var i=!1;for(e in t)t.hasOwnProperty(e)&&(r=this.write.apply(this,[e,this.pretty]),n=this.write.apply(this,[t[e],this.pretty]),null!=r&&null!=n&&(i&&o.push(","),o.push(this.writeNewline(),this.writeIndent(),r,":",this.writeSpace(),n),i=!0));return this.level-=1,o.push(this.writeNewline(),this.writeIndent(),"}"),o.join("")},array:function(t){var e,r=["["];this.level+=1;for(var n=0,o=t.length;n<o;++n)null!=(e=this.write.apply(this,[t[n],this.pretty]))&&(n>0&&r.push(","),r.push(this.writeNewline(),this.writeIndent(),e));return this.level-=1,r.push(this.writeNewline(),this.writeIndent(),"]"),r.join("")},string:function(t){var e={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};return/["\\\x00-\x1f]/.test(t)?'"'+t.replace(/([\x00-\x1f\\"])/g,function(t,r){var n=e[r];return n||(n=r.charCodeAt(),"\\u00"+Math.floor(n/16).toString(16)+(n%16).toString(16))})+'"':'"'+t+'"'},number:function(t){return isFinite(t)?String(t):"null"},boolean:function(t){return String(t)},date:function(t){function e(t){return t<10?"0"+t:t}return'"'+t.getFullYear()+"-"+e(t.getMonth()+1)+"-"+e(t.getDate())+"T"+e(t.getHours())+":"+e(t.getMinutes())+":"+e(t.getSeconds())+'"'}},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ze(t,e)}(e,He),r=e,(n=[{key:"read",value:function(t,e){var r;if(this.nativeJSON)try{r=JSON.parse(t,e)}catch(e){return{data:t}}return this.keepData&&(this.data=r),r}},{key:"write",value:function(t,e){this.pretty=!!e;var r=null,n=Ke(t);if(this.serialize[n])try{r=!this.pretty&&this.nativeJSON?JSON.stringify(t):this.serialize[n].apply(this,[t])}catch(t){}return r}},{key:"writeIndent",value:function(){var t=[];if(this.pretty)for(var e=0;e<this.level;++e)t.push(this.indent);return t.join("")}},{key:"writeNewline",value:function(){return this.pretty?this.newline:""}},{key:"writeSpace",value:function(){return this.pretty?this.space:""}}])&&Ye(r.prototype,n),o&&Ye(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function er(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?er(Object(r),!0).forEach(function(e){nr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nr(t,e,r){return(e=ar(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function or(t){"@babel/helpers - typeof";return(or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ir(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ar(n.key),n)}}function ar(t){var e=function(t,e){if("object"!=or(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=or(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==or(e)?e:e+""}var sr=function(){return t=function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=this;this.EVENT_TYPES=["processCompleted","processFailed"],this.events=null,this.eventListeners=null,this.url=null,this.urls=null,this.proxy=null,this.index=null,this.length=null,this.totalTimes=null,this.POLLING_TIMES=3,this.isInTheSameDomain=null,this.withCredentials=!1,j.isArray(e)?(n.urls=e,n.length=e.length,n.totalTimes=n.length,1===n.length?n.url=e[0]:(n.index=parseInt(Math.random()*n.length),n.url=e[n.index])):(n.totalTimes=1,n.url=e),j.isArray(e)&&!n.isServiceSupportPolling()&&(n.url=e[0],n.totalTimes=1),r=r||{},this.crossOrigin=r.crossOrigin,this.headers=r.headers,j.extend(this,r),n.isInTheSameDomain=j.isInTheSameDomain(n.url),n.events=new N(n,null,n.EVENT_TYPES,!0),n.eventListeners instanceof Object&&n.events.on(n.eventListeners),this.CLASS_NAME="SuperMap.CommonServiceBase"},(e=[{key:"destroy",value:function(){var t=this;j.isArray(t.urls)&&(t.urls=null,t.index=null,t.length=null,t.totalTimes=null),t.url=null,t.isInTheSameDomain=null,t.EVENT_TYPES=null,t.events&&(t.events.destroy(),t.events=null),t.eventListeners&&(t.eventListeners=null)}},{key:"request",value:function(t){var e=t.scope.format;if("string"==typeof t.success&&(t.scope.format=t.success,e=t.success,t.success=null,t.failure=null),e&&!this.supportDataFormat(e))throw new Error("".concat(this.CLASS_NAME," is not surport ").concat(e," format!"));var r=this;return t.url=t.url||r.url,this._returnContent(t)&&!t.url.includes("returnContent=true")&&(t.url=j.urlAppend(t.url,"returnContent=true")),t.proxy=t.proxy||r.proxy,t.withCredentials=void 0!=t.withCredentials?t.withCredentials:r.withCredentials,t.crossOrigin=void 0!=t.crossOrigin?t.crossOrigin:r.crossOrigin,t.headers=t.headers||r.headers,t.isInTheSameDomain=r.isInTheSameDomain,t.withoutFormatSuffix=t.scope.withoutFormatSuffix||!1,t.url=et.appendCredential(t.url),r.calculatePollingTimes(),t.scope=r,r.totalTimes>0?(r.totalTimes--,r.ajaxPolling(t)):r._commit(t)}},{key:"ajaxPolling",value:function(t){var e=this,r=t.url,n=/^http:\/\/([a-z]{9}|(\d+\.){3}\d+):\d{0,4}/;return e.index=parseInt(Math.random()*e.length),e.url=e.urls[e.index],r=r.replace(n,n.exec(e.url)[0]),t.url=r,t.isInTheSameDomain=j.isInTheSameDomain(r),e._commit(t)}},{key:"calculatePollingTimes",value:function(){var t=this;t.times?t.totalTimes>t.POLLING_TIMES?t.times>t.POLLING_TIMES?t.totalTimes=t.POLLING_TIMES:t.totalTimes=t.times:t.times<t.totalTimes&&(t.totalTimes=t.times):t.totalTimes>t.POLLING_TIMES&&(t.totalTimes=t.POLLING_TIMES),t.totalTimes--}},{key:"isServiceSupportPolling",value:function(){return!("SuperMap.REST.ThemeService"===this.CLASS_NAME||"SuperMap.REST.EditFeaturesService"===this.CLASS_NAME)}},{key:"transformResult",value:function(t,e){return{result:t=j.transformResult(t),options:e}}},{key:"transformErrorResult",value:function(t,e){return{error:(t=j.transformResult(t)).error||t,options:e}}},{key:"serviceProcessCompleted",value:function(t,e){t=this.transformResult(t).result,this.events.triggerEvent("processCompleted",{result:t,options:e})}},{key:"serviceProcessFailed",value:function(t,e){var r=(t=this.transformErrorResult(t).error).error||t;this.events.triggerEvent("processFailed",{error:r,options:e})}},{key:"_returnContent",value:function(t){return t.scope.format!==ot&&!!t.scope.returnContent}},{key:"supportDataFormat",value:function(t){return this.dataFormat().includes(t)}},{key:"dataFormat",value:function(){return[rt,nt]}},{key:"_commit",value:function(t){var e=this;if("POST"===t.method||"PUT"===t.method||"PATCH"===t.method)if(t.params&&(t.url=j.urlAppend(t.url,j.getParameterString(t.params||{}))),"object"!==or(t.data)||t.data instanceof FormData)t.params=t.data;else try{t.params=j.toJSON(t.data)}catch(t){console.log("不是json对象")}return H.commit(t.method,t.url,t.params,{headers:t.headers,withoutFormatSuffix:t.withoutFormatSuffix,withCredentials:t.withCredentials,crossOrigin:t.crossOrigin,timeout:t.async?0:null,proxy:t.proxy}).then(function(t){return t.text?t.text():t.json?t.json():t}).then(function(e){var r=e;return"string"==typeof e&&(r=(new tr).read(e)),(!r||r.error||r.code>=300&&304!==r.code)&&(r=r&&r.error?{error:r.error}:{error:r}),r&&t.scope.format===ot&&(r.newResourceLocation=r.newResourceLocation.replace(".json","")+".fgb"),r}).catch(function(t){return{error:t}}).then(function(r){var n={object:e};if(r.error){var o="processFailed";if(e.events&&e.events.listeners[o]&&e.events.listeners[o].length){var i=t.failure&&(t.scope?c(t.failure,t.scope):t.failure);i?i(r,t):e.serviceProcessFailed(r,t)}else(n=rr(rr({},n),e.transformErrorResult(r,t))).type=o,t.failure&&t.failure(n)}else{var a="processCompleted";if(e.events&&e.events.listeners[a]&&e.events.listeners[a].length){var s=t.success&&(t.scope?c(t.success,t.scope):t.success);s?s(r,t):e.serviceProcessCompleted(r,t)}else r.succeed=void 0==r.succeed||r.succeed,(n=rr(rr({},n),e.transformResult(r,t))).type=a,t.success&&t.success(n)}return n})}}])&&ir(t.prototype,e),r&&ir(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function ur(t){"@babel/helpers - typeof";return(ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lr(n.key),n)}}function lr(t){var e=function(t,e){if("object"!=ur(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ur(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ur(e)?e:e+""}function fr(t,e,r){return e=hr(e),function(t,e){if(e&&("object"==ur(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],hr(t).constructor):e.apply(t,r))}function pr(){return(pr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=hr(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function hr(t){return(hr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yr(t,e){return(yr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var dr=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=fr(this,e,[t,r])).options=r||{},n.CLASS_NAME="SuperMap.AddressMatchService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yr(t,e)}(e,sr),r=e,(n=[{key:"destroy",value:function(){var t,r,n,o,i;(t=e,r="destroy",n=this,i=pr(hr(1&(o=3)?t.prototype:t),r,n),2&o&&"function"==typeof i?function(t){return i.apply(n,t)}:i)([])}},{key:"code",value:function(t,e,r){if(e instanceof fe)return this.processAsync(t,e,r)}},{key:"decode",value:function(t,e,r){if(e instanceof de)return this.processAsync(t,e,r)}},{key:"processAsync",value:function(t,e,r){return this.request({method:"GET",url:t,params:e,scope:this,success:r,failure:r})}},{key:"transformResult",value:function(t,e){return t.succeed&&delete t.succeed,{result:t,options:e}}}])&&cr(r.prototype,n),o&&cr(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function mr(t){"@babel/helpers - typeof";return(mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function vr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,br(n.key),n)}}function br(t){var e=function(t,e){if("object"!=mr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=mr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==mr(e)?e:e+""}function gr(t,e,r){return e=Sr(e),function(t,e){if(e&&("object"==mr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Sr(t).constructor):e.apply(t,r))}function Sr(t){return(Sr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function wr(t,e){return(wr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Or=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=gr(this,e,[t,r])).CLASS_NAME="SuperMap.REST.AddressMatchService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wr(t,e)}(e,sr),r=e,(n=[{key:"code",value:function(t,e){var r=this;return new dr(r.url,{headers:r.headers,proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin}).code(r.url+"/geocoding",t,e)}},{key:"decode",value:function(t,e){var r=this;return new dr(r.url,{headers:r.headers,proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin}).decode(r.url+"/geodecoding",t,e)}}])&&vr(r.prototype,n),o&&vr(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function _r(t){"@babel/helpers - typeof";return(_r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Pr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Er(n.key),n)}}function Er(t){var e=function(t,e){if("object"!=_r(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_r(e)?e:e+""}function kr(t,e,r){return e=Tr(e),jr(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Tr(t).constructor):e.apply(t,r))}function jr(t,e){if(e&&("object"==_r(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function xr(){return(xr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Tr(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Tr(t){return(Tr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Cr(t,e){return(Cr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}Se.REST.AddressMatchService=Or;var Rr=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),n=kr(this,e,[t,r]),r?(n.datasource=null,n.dataset=null,r&&j.extend(n,r),n.CLASS_NAME="SuperMap.DatasetService",n):jr(n)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Cr(t,e)}(e,sr),r=e,(n=[{key:"destroy",value:function(){var t,r,n,o,i;(t=e,r="destroy",n=this,i=xr(Tr(1&(o=3)?t.prototype:t),r,n),2&o&&"function"==typeof i?function(t){return i.apply(n,t)}:i)([]);this.datasource=null,this.dataset=null}},{key:"getDatasetsService",value:function(t,e){var r=j.urlPathAppend(this.url,"datasources/name/".concat(t,"/datasets"));return this.processAsync(r,"GET",e)}},{key:"getDatasetService",value:function(t,e,r){var n=j.urlPathAppend(this.url,"datasources/name/".concat(t,"/datasets/name/").concat(e));return this.processAsync(n,"GET",r)}},{key:"setDatasetService",value:function(t,e){if(t){var r=j.urlPathAppend(this.url,"datasources/name/".concat(t.datasourceName,"/datasets/name/").concat(t.datasetName));return delete t.datasourceName,this.processAsync(r,"PUT",e,t)}}},{key:"deleteDatasetService",value:function(t,e,r){var n=j.urlPathAppend(this.url,"datasources/name/".concat(t,"/datasets/name/").concat(e));return this.processAsync(n,"DELETE",r)}},{key:"processAsync",value:function(t,e,r,n){var o={url:t,method:e,scope:this,success:r,failure:r};return n&&(o.data=j.toJSON(n)),this.request(o)}}])&&Pr(r.prototype,n),o&&Pr(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Ar(t){"@babel/helpers - typeof";return(Ar="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Nr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ir(n.key),n)}}function Ir(t){var e=function(t,e){if("object"!=Ar(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ar(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ar(e)?e:e+""}var Dr=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasourceName=null,this.datasetName=null,this.datasetType=null,e&&j.extend(this,e),this.CLASS_NAME="SuperMap.CreateDatasetParameters")},(e=[{key:"destroy",value:function(){this.datasourceName=null,this.datasetName=null,this.datasetType=null}}])&&Nr(t.prototype,e),r&&Nr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Mr(t){"@babel/helpers - typeof";return(Mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Lr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Jr(n.key),n)}}function Jr(t){var e=function(t,e){if("object"!=Mr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Mr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Mr(e)?e:e+""}var Fr=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasourceName=null,this.datasetName=null,this.isFileCache=null,this.description=null,this.prjCoordSys=null,this.charset=null,this.palette=null,this.noValue=null,e&&j.extend(this,e),this.CLASS_NAME="SuperMap.UpdateDatasetParameters")},(e=[{key:"destroy",value:function(){var t=this;t.datasourceName=null,t.datasetName=null,t.isFileCache=null,t.prjCoordSys=null,t.charset=null,t.palette=null,t.noValue=null}}])&&Lr(t.prototype,e),r&&Lr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Ur(t){"@babel/helpers - typeof";return(Ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Br(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zr(n.key),n)}}function zr(t){var e=function(t,e){if("object"!=Ur(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ur(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ur(e)?e:e+""}function Qr(t,e,r){return e=qr(e),function(t,e){if(e&&("object"==Ur(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],qr(t).constructor):e.apply(t,r))}function qr(t){return(qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Gr(t,e){return(Gr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Vr=function(t){function e(t,r){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var o=n=Qr(this,e,[t,r]);return n._datasetService=new Rr(o.url,{proxy:o.proxy,withCredentials:o.withCredentials,crossOrigin:o.crossOrigin,headers:o.headers}),n.CLASS_NAME="SuperMap.REST.DatasetService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Gr(t,e)}(e,sr),r=e,(n=[{key:"getDatasets",value:function(t,e){if(t)return this._datasetService.getDatasetsService(t,e)}},{key:"getDataset",value:function(t,e,r){if(t&&e)return this._datasetService.getDatasetService(t,e,r)}},{key:"setDataset",value:function(t,e){var r;if(t instanceof Dr||t instanceof Fr)return t instanceof Dr?r={datasetType:t.datasetType,datasourceName:t.datasourceName,datasetName:t.datasetName}:t instanceof Fr&&(r={datasetName:t.datasetName,datasourceName:t.datasourceName,isFileCache:t.isFileCache,description:t.description,prjCoordSys:t.prjCoordSys,charset:t.charset}),this._datasetService.setDatasetService(r,e)}},{key:"deleteDataset",value:function(t,e,r){return this._datasetService.deleteDatasetService(t,e,r)}}])&&Br(r.prototype,n),o&&Br(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Hr(t){"@babel/helpers - typeof";return(Hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Kr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Yr(n.key),n)}}function Yr(t){var e=function(t,e){if("object"!=Hr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Hr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Hr(e)?e:e+""}function Wr(t,e,r){return e=Xr(e),function(t,e){if(e&&("object"==Hr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Xr(t).constructor):e.apply(t,r))}function $r(){return($r="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Xr(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Xr(t){return(Xr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Zr(t,e){return(Zr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}Se.REST.DatasetService=Vr;var tn=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),n=Wr(this,e,[t,r]),r&&j.extend(n,r),n.CLASS_NAME="SuperMap.DatasourceService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Zr(t,e)}(e,sr),r=e,(n=[{key:"destroy",value:function(){var t,r,n,o,i;(t=e,r="destroy",n=this,i=$r(Xr(1&(o=3)?t.prototype:t),r,n),2&o&&"function"==typeof i?function(t){return i.apply(n,t)}:i)([])}},{key:"getDatasourceService",value:function(t,e){var r=j.urlPathAppend(this.url,"datasources/name/".concat(t));return this.processAsync(r,"GET",e)}},{key:"getDatasourcesService",value:function(t){var e=j.urlPathAppend(this.url,"datasources");return this.processAsync(e,"GET",t)}},{key:"setDatasourceService",value:function(t,e){if(t){var r=j.urlPathAppend(this.url,"datasources/name/".concat(t.datasourceName));return this.processAsync(r,"PUT",e,t)}}},{key:"processAsync",value:function(t,e,r,n){var o={url:t,method:e,scope:this,success:r,failure:r};return n&&(o.data=j.toJSON(n)),this.request(o)}}])&&Kr(r.prototype,n),o&&Kr(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function en(t){"@babel/helpers - typeof";return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nn(n.key),n)}}function nn(t){var e=function(t,e){if("object"!=en(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=en(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==en(e)?e:e+""}var on=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e&&(this.datasourceName=null,this.description=null,this.coordUnit=null,this.distanceUnit=null,e&&j.extend(this,e),this.CLASS_NAME="SuperMap.SetDatasourceParameters")},(e=[{key:"destroy",value:function(){this.datasourceName=null,this.description=null,this.coordUnit=null,this.distanceUnit=null}}])&&rn(t.prototype,e),r&&rn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function an(t){"@babel/helpers - typeof";return(an="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,un(n.key),n)}}function un(t){var e=function(t,e){if("object"!=an(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=an(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==an(e)?e:e+""}function cn(t,e,r){return e=ln(e),function(t,e){if(e&&("object"==an(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],ln(t).constructor):e.apply(t,r))}function ln(t){return(ln=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fn(t,e){return(fn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var pn=function(t){function e(t,r){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var o=n=cn(this,e,[t,r]);return n._datasourceService=new tn(o.url,{proxy:o.proxy,withCredentials:o.withCredentials,crossOrigin:o.crossOrigin,headers:o.headers}),n.CLASS_NAME="SuperMap.REST.DatasourceService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fn(t,e)}(e,sr),r=e,(n=[{key:"getDatasources",value:function(t){return this._datasourceService.getDatasourcesService(t)}},{key:"getDatasource",value:function(t,e){if(t)return this._datasourceService.getDatasourceService(t,e)}},{key:"setDatasource",value:function(t,e){if(t instanceof on){var r={description:t.description,coordUnit:t.coordUnit,distanceUnit:t.distanceUnit,datasourceName:t.datasourceName};return this._datasourceService.setDatasourceService(r,e)}}}])&&sn(r.prototype,n),o&&sn(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function hn(t){"@babel/helpers - typeof";return(hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dn(n.key),n)}}function dn(t){var e=function(t,e){if("object"!=hn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hn(e)?e:e+""}function mn(t,e,r){return e=bn(e),function(t,e){if(e&&("object"==hn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],bn(t).constructor):e.apply(t,r))}function vn(){return(vn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=bn(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function bn(t){return(bn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function gn(t,e){return(gn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}Se.REST.DatasourceService=pn;var Sn=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=mn(this,e,[t,r=r||{}])).CLASS_NAME="SuperMap.ProcessingServiceBase",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gn(t,e)}(e,sr),r=e,(n=[{key:"destroy",value:function(){var t,r,n,o,i;(t=e,r="destroy",n=this,i=vn(bn(1&(o=3)?t.prototype:t),r,n),2&o&&"function"==typeof i?function(t){return i.apply(n,t)}:i)([])}},{key:"getJobs",value:function(t,e){var r=this;return H.get(et.appendCredential(t),null,{proxy:r.proxy}).then(function(t){return t.json()}).then(function(t){var n={result:t,object:r,type:"processCompleted"};return e(n),n}).catch(function(t){var n={error:t,object:r,type:"processFailed"};return e(n),n})}},{key:"addJob",value:function(t,e,r,n,o,i){var a=this,s=null;e&&e instanceof r&&(s=new Object,r.toObject(e,s));var u=Object.assign({"Content-Type":"application/x-www-form-urlencoded"},a.headers||{}),c={proxy:a.proxy,headers:u,withCredentials:a.withCredentials,crossOrigin:a.crossOrigin,isInTheSameDomain:a.isInTheSameDomain};return H.post(et.appendCredential(t),JSON.stringify(s),c).then(function(t){return t.json()}).then(function(t){return t.succeed?a.transformResult(t,n,o,i):((t=a.transformErrorResult(t)).options=a,t.type="processFailed",o(t),t)}).catch(function(t){return(t=a.transformErrorResult({error:t})).options=a,t.type="processFailed",o(t),t})}},{key:"transformResult",value:function(t,e,r,n){t=j.transformResult(t),e=e||1e3;var o=this;if(t)return new Promise(function(i){var a=setInterval(function(){H.get(et.appendCredential(t.newResourceLocation),{_t:(new Date).getTime()}).then(function(t){return t.json()}).then(function(t){if(i({object:o,id:t.id,state:t.state}),n({id:t.id,state:t.state,object:o}),"LOST"===t.state.runState||"KILLED"===t.state.runState||"FAILED"===t.state.runState){clearInterval(a);var e={error:t.state.errorMsg,state:t.state.runState,object:o,type:"processFailed"};i(e),r(e)}if("FINISHED"===t.state.runState&&t.setting.serviceInfo){clearInterval(a);var s={result:t,object:o,type:"processCompleted"};i(s),r(s)}}).catch(function(t){clearInterval(a);var e={error:t,object:o,type:"processFailed"};i(e),r(e)})},e)})}}])&&yn(r.prototype,n),o&&yn(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function wn(t){"@babel/helpers - typeof";return(wn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function On(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_n(n.key),n)}}function _n(t){var e=function(t,e){if("object"!=wn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=wn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==wn(e)?e:e+""}function Pn(t,e,r){return e=jn(e),function(t,e){if(e&&("object"==wn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],jn(t).constructor):e.apply(t,r))}function En(t,e,r,n){var o=kn(jn(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function kn(){return(kn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=jn(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function jn(t){return(jn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function xn(t,e){return(xn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Tn=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Pn(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/density"),n.CLASS_NAME="SuperMap.KernelDensityJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xn(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){En(e,"destroy",this,3)([])}},{key:"getKernelDensityJobs",value:function(t){return En(e,"getJobs",this,3)([this.url,t])}},{key:"getKernelDensityJob",value:function(t,r){return En(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addKernelDensityJob",value:function(t,r,n,o){return En(e,"addJob",this,3)([this.url,t,Nt,r,n,o])}}])&&On(r.prototype,n),o&&On(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Cn(t){"@babel/helpers - typeof";return(Cn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Rn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,An(n.key),n)}}function An(t){var e=function(t,e){if("object"!=Cn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Cn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Cn(e)?e:e+""}function Nn(t,e,r){return e=Mn(e),function(t,e){if(e&&("object"==Cn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Mn(t).constructor):e.apply(t,r))}function In(t,e,r,n){var o=Dn(Mn(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Dn(){return(Dn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Mn(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Mn(t){return(Mn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ln(t,e){return(Ln=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Jn=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Nn(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/query"),n.CLASS_NAME="SuperMap.SingleObjectQueryJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ln(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){In(e,"destroy",this,3)([])}},{key:"getQueryJobs",value:function(t){return In(e,"getJobs",this,3)([this.url,t])}},{key:"getQueryJob",value:function(t,r){return In(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addQueryJob",value:function(t,r,n,o){return In(e,"addJob",this,3)([this.url,t,Lt,r,n,o])}}])&&Rn(r.prototype,n),o&&Rn(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Fn(t){"@babel/helpers - typeof";return(Fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Un(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Bn(n.key),n)}}function Bn(t){var e=function(t,e){if("object"!=Fn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Fn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Fn(e)?e:e+""}function zn(t,e,r){return e=Gn(e),function(t,e){if(e&&("object"==Fn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Gn(t).constructor):e.apply(t,r))}function Qn(t,e,r,n){var o=qn(Gn(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function qn(){return(qn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Gn(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Gn(t){return(Gn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Vn(t,e){return(Vn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Hn=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=zn(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/aggregatepoints"),n.CLASS_NAME="SuperMap.SummaryMeshJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Vn(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){Qn(e,"destroy",this,3)([])}},{key:"getSummaryMeshJobs",value:function(t){return Qn(e,"getJobs",this,3)([this.url,t])}},{key:"getSummaryMeshJob",value:function(t,r){return Qn(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addSummaryMeshJob",value:function(t,r,n,o){return Qn(e,"addJob",this,3)([this.url,t,Gt,r,n,o])}}])&&Un(r.prototype,n),o&&Un(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Kn(t){"@babel/helpers - typeof";return(Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Yn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Wn(n.key),n)}}function Wn(t){var e=function(t,e){if("object"!=Kn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Kn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Kn(e)?e:e+""}function $n(t,e,r){return e=to(e),function(t,e){if(e&&("object"==Kn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],to(t).constructor):e.apply(t,r))}function Xn(t,e,r,n){var o=Zn(to(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Zn(){return(Zn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=to(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function to(t){return(to=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eo(t,e){return(eo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var ro=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=$n(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/vectorclip"),n.CLASS_NAME="SuperMap.VectorClipJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eo(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){Xn(e,"destroy",this,3)([])}},{key:"getVectorClipJobs",value:function(t){return Xn(e,"getJobs",this,3)([this.url,t])}},{key:"getVectorClipJob",value:function(t,r){return Xn(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addVectorClipJob",value:function(t,r,n,o){return Xn(e,"addJob",this,3)([this.url,t,ge,r,n,o])}}])&&Yn(r.prototype,n),o&&Yn(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function no(t){"@babel/helpers - typeof";return(no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function oo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,io(n.key),n)}}function io(t){var e=function(t,e){if("object"!=no(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=no(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==no(e)?e:e+""}function ao(t,e,r){return e=co(e),function(t,e){if(e&&("object"==no(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],co(t).constructor):e.apply(t,r))}function so(t,e,r,n){var o=uo(co(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function uo(){return(uo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=co(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function co(t){return(co=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lo(t,e){return(lo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var fo=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=ao(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/overlay"),n.CLASS_NAME="SuperMap.OverlayGeoJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lo(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){so(e,"destroy",this,3)([])}},{key:"getOverlayGeoJobs",value:function(t){return so(e,"getJobs",this,3)([this.url,t])}},{key:"getOverlayGeoJob",value:function(t,r){return so(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addOverlayGeoJob",value:function(t,r,n,o){return so(e,"addJob",this,3)([this.url,t,Zt,r,n,o])}}])&&oo(r.prototype,n),o&&oo(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function po(t){"@babel/helpers - typeof";return(po="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ho(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yo(n.key),n)}}function yo(t){var e=function(t,e){if("object"!=po(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=po(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==po(e)?e:e+""}function mo(t,e,r){return e=go(e),function(t,e){if(e&&("object"==po(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],go(t).constructor):e.apply(t,r))}function vo(t,e,r,n){var o=bo(go(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function bo(){return(bo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=go(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function go(t){return(go=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function So(t,e){return(So=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var wo=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=mo(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/summaryregion"),n.CLASS_NAME="SuperMap.SummaryRegionJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&So(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){vo(e,"destroy",this,3)([])}},{key:"getSummaryRegionJobs",value:function(t){return vo(e,"getJobs",this,3)([this.url,t])}},{key:"getSummaryRegionJob",value:function(t,r){return vo(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addSummaryRegionJob",value:function(t,r,n,o){return vo(e,"addJob",this,3)([this.url,t,Yt,r,n,o])}}])&&ho(r.prototype,n),o&&ho(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Oo(t){"@babel/helpers - typeof";return(Oo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Po(n.key),n)}}function Po(t){var e=function(t,e){if("object"!=Oo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Oo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Oo(e)?e:e+""}function Eo(t,e,r){return e=xo(e),function(t,e){if(e&&("object"==Oo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],xo(t).constructor):e.apply(t,r))}function ko(t,e,r,n){var o=jo(xo(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function jo(){return(jo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=xo(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function xo(t){return(xo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function To(t,e){return(To=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Co=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Eo(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/buffers"),n.CLASS_NAME="SuperMap.BuffersAnalystJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&To(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){ko(e,"destroy",this,3)([])}},{key:"getBuffersJobs",value:function(t){return ko(e,"getJobs",this,3)([this.url,t])}},{key:"getBuffersJob",value:function(t,r){return ko(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addBuffersJob",value:function(t,r,n,o){return ko(e,"addJob",this,3)([this.url,t,ne,r,n,o])}}])&&_o(r.prototype,n),o&&_o(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Ro(t){"@babel/helpers - typeof";return(Ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ao(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,No(n.key),n)}}function No(t){var e=function(t,e){if("object"!=Ro(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ro(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ro(e)?e:e+""}function Io(t,e,r){return e=Lo(e),function(t,e){if(e&&("object"==Ro(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Lo(t).constructor):e.apply(t,r))}function Do(t,e,r,n){var o=Mo(Lo(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Mo(){return(Mo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Lo(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Lo(t){return(Lo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Jo(t,e){return(Jo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Fo=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Io(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/topologyvalidator"),n.CLASS_NAME="SuperMap.TopologyValidatorJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Jo(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){Do(e,"destroy",this,3)([])}},{key:"getTopologyValidatorJobs",value:function(t){return Do(e,"getJobs",this,3)([this.url,t])}},{key:"getTopologyValidatorJob",value:function(t,r){return Do(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addTopologyValidatorJob",value:function(t,r,n,o){return Do(e,"addJob",this,3)([this.url,t,se,r,n,o])}}])&&Ao(r.prototype,n),o&&Ao(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Uo(t){"@babel/helpers - typeof";return(Uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Bo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zo(n.key),n)}}function zo(t){var e=function(t,e){if("object"!=Uo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Uo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Uo(e)?e:e+""}function Qo(t,e,r){return e=Vo(e),function(t,e){if(e&&("object"==Uo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,function(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return function(){return!!t}()}()?Reflect.construct(e,r||[],Vo(t).constructor):e.apply(t,r))}function qo(t,e,r,n){var o=Go(Vo(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function Go(){return(Go="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Vo(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}}).apply(null,arguments)}function Vo(t){return(Vo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ho(t,e){return(Ho=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var Ko=function(t){function e(t,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=Qo(this,e,[t,r])).url=j.urlPathAppend(n.url,"spatialanalyst/summaryattributes"),n.CLASS_NAME="SuperMap.SummaryAttributesJobsService",n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ho(t,e)}(e,Sn),r=e,(n=[{key:"destroy",value:function(){qo(e,"destroy",this,3)([])}},{key:"getSummaryAttributesJobs",value:function(t){return qo(e,"getJobs",this,3)([this.url,t])}},{key:"getSummaryAttributesJob",value:function(t,r){return qo(e,"getJobs",this,3)([j.urlPathAppend(this.url,t),r])}},{key:"addSummaryAttributesJob",value:function(t,r,n,o){return qo(e,"addJob",this,3)([this.url,t,Bt,r,n,o])}}])&&Bo(r.prototype,n),o&&Bo(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}();function Yo(t){"@babel/helpers - typeof";return(Yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Wo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$o(n.key),n)}}function $o(t){var e=function(t,e){if("object"!=Yo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Yo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Yo(e)?e:e+""}var Xo=function(){return t=function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.url=e,this.options=r||{},this.kernelDensityJobs={},this.summaryMeshJobs={},this.queryJobs={},this.summaryRegionJobs={},this.vectorClipJobs={},this.overlayGeoJobs={},this.buffersJobs={},this.topologyValidatorJobs={},this.summaryAttributesJobs={}},(e=[{key:"getKernelDensityJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Tn(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getKernelDensityJobs(t)}},{key:"getKernelDensityJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Tn(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getKernelDensityJob(t,e)}},{key:"addKernelDensityJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Tn(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addKernelDensityJob(t,r,e,function(t){o.kernelDensityJobs[t.id]=t.state})}},{key:"getKernelDensityJobState",value:function(t){return this.kernelDensityJobs[t]}},{key:"getSummaryMeshJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Hn(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getSummaryMeshJobs(t)}},{key:"getSummaryMeshJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Hn(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getSummaryMeshJob(t,e)}},{key:"addSummaryMeshJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Hn(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addSummaryMeshJob(t,r,e,function(t){o.summaryMeshJobs[t.id]=t.state})}},{key:"getSummaryMeshJobState",value:function(t){return this.summaryMeshJobs[t]}},{key:"getQueryJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Jn(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getQueryJobs(t)}},{key:"getQueryJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Jn(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getQueryJob(t,e)}},{key:"addQueryJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Jn(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addQueryJob(t,r,e,function(t){o.queryJobs[t.id]=t.state})}},{key:"getQueryJobState",value:function(t){return this.queryJobs[t]}},{key:"getSummaryRegionJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new wo(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getSummaryRegionJobs(t)}},{key:"getSummaryRegionJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new wo(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getSummaryRegionJob(t,e)}},{key:"addSummaryRegionJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new wo(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addSummaryRegionJob(t,r,e,function(t){o.summaryRegionJobs[t.id]=t.state})}},{key:"getSummaryRegionJobState",value:function(t){return this.summaryRegionJobs[t]}},{key:"getVectorClipJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new ro(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getVectorClipJobs(t)}},{key:"getVectorClipJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new ro(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getVectorClipJob(t,e)}},{key:"addVectorClipJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new ro(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addVectorClipJob(t,r,e,function(t){o.vectorClipJobs[t.id]=t.state})}},{key:"getVectorClipJobState",value:function(t){return this.vectorClipJobs[t]}},{key:"getOverlayGeoJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new fo(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getOverlayGeoJobs(t)}},{key:"getOverlayGeoJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new fo(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getOverlayGeoJob(t,e)}},{key:"addOverlayGeoJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new fo(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addOverlayGeoJob(t,r,e,function(t){o.overlayGeoJobs[t.id]=t.state})}},{key:"getoverlayGeoJobState",value:function(t){return this.overlayGeoJobs[t]}},{key:"getBuffersJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Co(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getBuffersJobs(t)}},{key:"getBuffersJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Co(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getBuffersJob(t,e)}},{key:"addBuffersJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Co(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addBuffersJob(t,r,e,function(t){o.buffersJobs[t.id]=t.state})}},{key:"getBuffersJobState",value:function(t){return this.buffersJobs[t]}},{key:"getTopologyValidatorJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Fo(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getTopologyValidatorJobs(t)}},{key:"getTopologyValidatorJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Fo(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getTopologyValidatorJob(t,e)}},{key:"addTopologyValidatorJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Fo(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addTopologyValidatorJob(t,r,e,function(t){o.topologyValidatorJobs[t.id]=t.state})}},{key:"getTopologyValidatorJobState",value:function(t){return this.topologyValidatorJobs[t]}},{key:"getSummaryAttributesJobs",value:function(t,e){var r=this,n=r._processFormat(e);return new Ko(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:n}).getSummaryAttributesJobs(t)}},{key:"getSummaryAttributesJob",value:function(t,e,r){var n=this,o=n._processFormat(r);return new Ko(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:o}).getSummaryAttributesJob(t,e)}},{key:"addSummaryAttributesJob",value:function(t,e,r,n){var o=this,i=o._processFormat(n);return new Ko(o.url,{proxy:o.options.proxy,withCredentials:o.options.withCredentials,crossOrigin:o.options.crossOrigin,headers:o.options.headers,format:i}).addSummaryAttributesJob(t,r,e,function(t){o.summaryAttributesJobs[t.id]=t.state})}},{key:"getSummaryAttributesJobState",value:function(t){return this.summaryAttributesJobs[t]}},{key:"_processFormat",value:function(t){return t||rt}}])&&Wo(t.prototype,e),r&&Wo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function Zo(t){"@babel/helpers - typeof";return(Zo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ti(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ei(n.key),n)}}function ei(t){var e=function(t,e){if("object"!=Zo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Zo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Zo(e)?e:e+""}var ri=function(){return t=function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._processingService=new Xo(e,r)},(e=[{key:"getKernelDensityJobs",value:function(t,e){return this._processingService.getKernelDensityJobs(t,e)}},{key:"getKernelDensityJob",value:function(t,e,r){return this._processingService.getKernelDensityJob(t,e,r)}},{key:"addKernelDensityJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addKernelDensityJob(t,e,r,n)}},{key:"getKernelDensityJobState",value:function(t){return this._processingService.getKernelDensityJobState(t)}},{key:"getSummaryMeshJobs",value:function(t,e){return this._processingService.getSummaryMeshJobs(t,e)}},{key:"getSummaryMeshJob",value:function(t,e,r){return this._processingService.getSummaryMeshJob(t,e,r)}},{key:"addSummaryMeshJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addSummaryMeshJob(t,e,r,n)}},{key:"getSummaryMeshJobState",value:function(t){return this._processingService.getSummaryMeshJobState(t)}},{key:"getQueryJobs",value:function(t,e){return this._processingService.getQueryJobs(t,e)}},{key:"getQueryJob",value:function(t,e,r){return this._processingService.getQueryJob(t,e,r)}},{key:"addQueryJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addQueryJob(t,e,r,n)}},{key:"getQueryJobState",value:function(t){return this._processingService.getQueryJobState(t)}},{key:"getSummaryRegionJobs",value:function(t,e){return this._processingService.getSummaryRegionJobs(t,e)}},{key:"getSummaryRegionJob",value:function(t,e,r){return this._processingService.getSummaryRegionJob(t,e,r)}},{key:"addSummaryRegionJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addSummaryRegionJob(t,e,r,n)}},{key:"getSummaryRegionJobState",value:function(t){return this._processingService.getSummaryRegionJobState(t)}},{key:"getVectorClipJobs",value:function(t,e){return this._processingService.getVectorClipJobs(t,e)}},{key:"getVectorClipJob",value:function(t,e,r){return this._processingService.getVectorClipJob(t,e,r)}},{key:"addVectorClipJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addVectorClipJob(t,e,r,n)}},{key:"getVectorClipJobState",value:function(t){return this._processingService.getVectorClipJobState(t)}},{key:"getOverlayGeoJobs",value:function(t,e){return this._processingService.getOverlayGeoJobs(t,e)}},{key:"getOverlayGeoJob",value:function(t,e,r){return this._processingService.getOverlayGeoJob(t,e,r)}},{key:"addOverlayGeoJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addOverlayGeoJob(t,e,r,n)}},{key:"getoverlayGeoJobState",value:function(t){return this._processingService.getoverlayGeoJobState(t)}},{key:"getBuffersJobs",value:function(t,e){return this._processingService.getBuffersJobs(t,e)}},{key:"getBuffersJob",value:function(t,e,r){return this._processingService.getBuffersJob(t,e,r)}},{key:"addBuffersJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addBuffersJob(t,e,r,n)}},{key:"getBuffersJobState",value:function(t){return this._processingService.getBuffersJobState(t)}},{key:"getTopologyValidatorJobs",value:function(t,e){return this._processingService.getTopologyValidatorJobs(t,e)}},{key:"getTopologyValidatorJob",value:function(t,e,r){return this._processingService.getTopologyValidatorJob(t,e,r)}},{key:"addTopologyValidatorJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addTopologyValidatorJob(t,e,r,n)}},{key:"getTopologyValidatorJobState",value:function(t){return this._processingService.getTopologyValidatorJobState(t)}},{key:"getSummaryAttributesJobs",value:function(t,e){return this._processingService.getSummaryAttributesJobs(t,e)}},{key:"getSummaryAttributesJob",value:function(t,e,r){return this._processingService.getSummaryAttributesJob(t,e,r)}},{key:"addSummaryAttributesJob",value:function(t,e,r,n){return t=this._processParams(t),this._processingService.addSummaryAttributesJob(t,e,r,n)}},{key:"getSummaryAttributesJobState",value:function(t){return this._processingService.getSummaryAttributesJobState(t)}},{key:"_processFormat",value:function(t){return t||rt}},{key:"_processParams",value:function(t){return t?(t.geometryQuery&&(t.geometryQuery=this._convertPatams(t.geometryQuery)),t.geometryClip&&(t.geometryClip=this._convertPatams(t.geometryClip)),t):{}}},{key:"_convertPatams",value:function(t){var e={};if(t.length<1)e="";else{for(var r=[],n=0;n<t.length;n++){var o={};o.x=t[n].x,o.y=t[n].y,r.push(o)}e.type="REGION",e.points=r}return e}}])&&ti(t.prototype,e),r&&ti(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function ni(t){"@babel/helpers - typeof";return(ni="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function oi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ii(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?oi(Object(r),!0).forEach(function(e){ai(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):oi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ai(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=ni(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ni(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ni(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Se.REST.ProcessingService=ri,Se.ElasticSearch=B,Se.SecurityManager=et,Se.VectorClipJobsParameter=ge,Se.KernelDensityJobParameter=Nt,Se.SingleObjectQueryJobsParameter=Lt,Se.SummaryAttributesJobsParameter=Bt,Se.SummaryMeshJobParameter=Gt,Se.SummaryRegionJobParameter=Yt,Se.OverlayGeoJobParameter=Zt,Se.BuffersAnalystJobsParameter=ne,Se.TopologyValidatorJobsParameter=se,Se.OutputSetting=Et,Se.MappingParameters=Tt,Se.GeoCodingParameter=fe,Se.GeoDecodingParameter=de,Se.FetchRequest=H,Se.Util=ii(ii({},Se.Util),j)},3416:function(t,e,r){"use strict";var n=r(3725);t.exports=n},5641:function(t,e,r){"use strict";var n=r(756);t.exports=n},1606:function(t,e,r){"use strict";r(6186);var n=r(7520);t.exports=n.Object.assign},379:function(t,e,r){"use strict";r(9319);var n=r(8490);t.exports=n.f("asyncIterator")},3873:function(t,e,r){"use strict";var n=r(5164),o=r(6390),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},5546:function(t,e,r){"use strict";var n=r(5343),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},5282:function(t,e,r){"use strict";var n=r(1212),o=r(9603),i=r(8765),a=function(t){return function(e,r,a){var s=n(e),u=i(s);if(0===u)return!t&&-1;var c,l=o(a,u);if(t&&r!=r){for(;u>l;)if((c=s[l++])!=c)return!0}else for(;u>l;l++)if((t||l in s)&&s[l]===r)return t||l||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},7953:function(t,e,r){"use strict";var n=r(7889),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},4017:function(t,e,r){"use strict";var n=r(2770),o=r(6608),i=r(3880),a=r(754);t.exports=function(t,e,r){for(var s=o(e),u=a.f,c=i.f,l=0;l<s.length;l++){var f=s[l];n(t,f)||r&&n(r,f)||u(t,f,c(e,f))}}},7592:function(t,e,r){"use strict";var n=r(4797),o=r(754),i=r(19);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},19:function(t){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4197:function(t,e,r){"use strict";var n=r(5164),o=r(754),i=r(6514),a=r(9998);t.exports=function(t,e,r,s){s||(s={});var u=s.enumerable,c=void 0!==s.name?s.name:e;if(n(r)&&i(r,c,s),s.global)u?t[e]=r:a(e,r);else{try{s.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:o.f(t,e,{value:r,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return t}},9998:function(t,e,r){"use strict";var n=r(8029),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},4797:function(t,e,r){"use strict";var n=r(3634);t.exports=!n(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},9750:function(t,e,r){"use strict";var n=r(8029),o=r(5343),i=n.document,a=o(i)&&o(i.createElement);t.exports=function(t){return a?i.createElement(t):{}}},3690:function(t){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2940:function(t,e,r){"use strict";var n=r(8029).navigator,o=n&&n.userAgent;t.exports=o?String(o):""},2944:function(t,e,r){"use strict";var n,o,i=r(8029),a=r(2940),s=i.process,u=i.Deno,c=s&&s.versions||u&&u.version,l=c&&c.v8;l&&(o=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},3485:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=r(8029),i=r(3880).f,a=r(7592),s=r(4197),u=r(9998),c=r(4017),l=r(4857);t.exports=function(t,e){var r,f,p,h,y,d=t.target,m=t.global,v=t.stat;if(r=m?o:v?o[d]||u(d,{}):o[d]&&o[d].prototype)for(f in e){if(h=e[f],p=t.dontCallGetSet?(y=i(r,f))&&y.value:r[f],!l(m?f:d+(v?".":"#")+f,t.forced)&&void 0!==p){if(n(h)==n(p))continue;c(h,p)}(t.sham||p&&p.sham)&&a(h,"sham",!0),s(r,f,h,t)}}},3634:function(t){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},8607:function(t,e,r){"use strict";var n=r(3634);t.exports=!n(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},3736:function(t,e,r){"use strict";var n=r(8607),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},9071:function(t,e,r){"use strict";var n=r(4797),o=r(2770),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,s=o(i,"name"),u=s&&"something"===function(){}.name,c=s&&(!n||n&&a(i,"name").configurable);t.exports={EXISTS:s,PROPER:u,CONFIGURABLE:c}},7889:function(t,e,r){"use strict";var n=r(8607),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);t.exports=n?a:function(t){return function(){return i.apply(t,arguments)}}},148:function(t,e,r){"use strict";var n=r(8029),o=r(5164);t.exports=function(t,e){return arguments.length<2?(r=n[t],o(r)?r:void 0):n[t]&&n[t][e];var r}},4897:function(t,e,r){"use strict";var n=r(3873),o=r(4350);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},8029:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=function(t){return t&&t.Math===Math&&t};t.exports=o("object"==("undefined"==typeof globalThis?"undefined":n(globalThis))&&globalThis)||o("object"==("undefined"==typeof window?"undefined":n(window))&&window)||o("object"==("undefined"==typeof self?"undefined":n(self))&&self)||o("object"==(void 0===r.g?"undefined":n(r.g))&&r.g)||o("object"==n(this)&&this)||function(){return this}()||Function("return this")()},2770:function(t,e,r){"use strict";var n=r(7889),o=r(9452),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},7076:function(t){"use strict";t.exports={}},6182:function(t,e,r){"use strict";var n=r(4797),o=r(3634),i=r(9750);t.exports=!n&&!o(function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a})},4384:function(t,e,r){"use strict";var n=r(7889),o=r(3634),i=r(7953),a=Object,s=n("".split);t.exports=o(function(){return!a("z").propertyIsEnumerable(0)})?function(t){return"String"===i(t)?s(t,""):a(t)}:a},9413:function(t,e,r){"use strict";var n=r(7889),o=r(5164),i=r(3094),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return a(t)}),t.exports=i.inspectSource},6814:function(t,e,r){"use strict";var n,o,i,a=r(6185),s=r(8029),u=r(5343),c=r(7592),l=r(2770),f=r(3094),p=r(56),h=r(7076),y=s.TypeError,d=s.WeakMap;if(a||f.state){var m=f.state||(f.state=new d);m.get=m.get,m.has=m.has,m.set=m.set,n=function(t,e){if(m.has(t))throw new y("Object already initialized");return e.facade=t,m.set(t,e),e},o=function(t){return m.get(t)||{}},i=function(t){return m.has(t)}}else{var v=p("state");h[v]=!0,n=function(t,e){if(l(t,v))throw new y("Object already initialized");return e.facade=t,c(t,v,e),e},o=function(t){return l(t,v)?t[v]:{}},i=function(t){return l(t,v)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=o(e)).type!==t)throw new y("Incompatible receiver, "+t+" required");return r}}}},5164:function(t){"use strict";function e(t){"@babel/helpers - typeof";return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r="object"==("undefined"==typeof document?"undefined":e(document))&&document.all;t.exports=void 0===r&&void 0!==r?function(t){return"function"==typeof t||t===r}:function(t){return"function"==typeof t}},4857:function(t,e,r){"use strict";var n=r(3634),o=r(5164),i=/#|\.prototype\./,a=function(t,e){var r=u[s(t)];return r===l||r!==c&&(o(e)?n(e):!!e)},s=a.normalize=function(t){return String(t).replace(i,".").toLowerCase()},u=a.data={},c=a.NATIVE="N",l=a.POLYFILL="P";t.exports=a},4350:function(t){"use strict";t.exports=function(t){return null===t||void 0===t}},5343:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=r(5164);t.exports=function(t){return"object"==n(t)?null!==t:o(t)}},2358:function(t){"use strict";t.exports=!1},5648:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=r(148),i=r(5164),a=r(6466),s=r(2865),u=Object;t.exports=s?function(t){return"symbol"==n(t)}:function(t){var e=o("Symbol");return i(e)&&a(e.prototype,u(t))}},8765:function(t,e,r){"use strict";var n=r(1995);t.exports=function(t){return n(t.length)}},6514:function(t,e,r){"use strict";var n=r(7889),o=r(3634),i=r(5164),a=r(2770),s=r(4797),u=r(9071).CONFIGURABLE,c=r(9413),l=r(6814),f=l.enforce,p=l.get,h=String,y=Object.defineProperty,d=n("".slice),m=n("".replace),v=n([].join),b=s&&!o(function(){return 8!==y(function(){},"length",{value:8}).length}),g=String(String).split("String"),S=t.exports=function(t,e,r){"Symbol("===d(h(e),0,7)&&(e="["+m(h(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(s?y(t,"name",{value:e,configurable:!0}):t.name=e),b&&r&&a(r,"arity")&&t.length!==r.arity&&y(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?s&&y(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=f(t);return a(n,"source")||(n.source=v(g,"string"==typeof e?e:"")),t};Function.prototype.toString=S(function(){return i(this)&&p(this).source||c(this)},"toString")},4433:function(t){"use strict";var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},8192:function(t,e,r){"use strict";var n=r(4797),o=r(7889),i=r(3736),a=r(3634),s=r(1185),u=r(7260),c=r(128),l=r(9452),f=r(4384),p=Object.assign,h=Object.defineProperty,y=o([].concat);t.exports=!p||a(function(){if(n&&1!==p({b:1},p(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol("assign detection");return t[r]=7,"abcdefghijklmnopqrst".split("").forEach(function(t){e[t]=t}),7!==p({},t)[r]||"abcdefghijklmnopqrst"!==s(p({},e)).join("")})?function(t,e){for(var r=l(t),o=arguments.length,a=1,p=u.f,h=c.f;o>a;)for(var d,m=f(arguments[a++]),v=p?y(s(m),p(m)):s(m),b=v.length,g=0;b>g;)d=v[g++],n&&!i(h,m,d)||(r[d]=m[d]);return r}:p},754:function(t,e,r){"use strict";var n=r(4797),o=r(6182),i=r(1363),a=r(5546),s=r(8),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor;e.f=n?i?function(t,e,r){if(a(t),e=s(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&"writable"in r&&!r.writable){var n=l(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:"configurable"in r?r.configurable:n.configurable,enumerable:"enumerable"in r?r.enumerable:n.enumerable,writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=s(e),a(r),o)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},3880:function(t,e,r){"use strict";var n=r(4797),o=r(3736),i=r(128),a=r(19),s=r(1212),u=r(8),c=r(2770),l=r(6182),f=Object.getOwnPropertyDescriptor;e.f=n?f:function(t,e){if(t=s(t),e=u(e),l)try{return f(t,e)}catch(t){}if(c(t,e))return a(!o(i.f,t,e),t[e])}},125:function(t,e,r){"use strict";var n=r(3763),o=r(3690).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},7260:function(t,e){"use strict";e.f=Object.getOwnPropertySymbols},6466:function(t,e,r){"use strict";var n=r(7889);t.exports=n({}.isPrototypeOf)},3763:function(t,e,r){"use strict";var n=r(7889),o=r(2770),i=r(1212),a=r(5282).indexOf,s=r(7076),u=n([].push);t.exports=function(t,e){var r,n=i(t),c=0,l=[];for(r in n)!o(s,r)&&o(n,r)&&u(l,r);for(;e.length>c;)o(n,r=e[c++])&&(~a(l,r)||u(l,r));return l}},1185:function(t,e,r){"use strict";var n=r(3763),o=r(3690);t.exports=Object.keys||function(t){return n(t,o)}},128:function(t,e){"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},8367:function(t,e,r){"use strict";var n=r(3736),o=r(5164),i=r(5343),a=TypeError;t.exports=function(t,e){var r,s;if("string"===e&&o(r=t.toString)&&!i(s=n(r,t)))return s;if(o(r=t.valueOf)&&!i(s=n(r,t)))return s;if("string"!==e&&o(r=t.toString)&&!i(s=n(r,t)))return s;throw new a("Can't convert object to primitive value")}},6608:function(t,e,r){"use strict";var n=r(148),o=r(7889),i=r(125),a=r(7260),s=r(5546),u=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(s(t)),r=a.f;return r?u(e,r(t)):e}},7520:function(t,e,r){"use strict";var n=r(8029);t.exports=n},1365:function(t,e,r){"use strict";var n=r(4350),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},56:function(t,e,r){"use strict";var n=r(1930),o=r(6177),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},3094:function(t,e,r){"use strict";var n=r(2358),o=r(8029),i=r(9998),a=t.exports=o["__core-js_shared__"]||i("__core-js_shared__",{});(a.versions||(a.versions=[])).push({version:"3.39.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},1930:function(t,e,r){"use strict";var n=r(3094);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},6576:function(t,e,r){"use strict";var n=r(2944),o=r(3634),i=r(8029).String;t.exports=!!Object.getOwnPropertySymbols&&!o(function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41})},9603:function(t,e,r){"use strict";var n=r(3784),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},1212:function(t,e,r){"use strict";var n=r(4384),o=r(1365);t.exports=function(t){return n(o(t))}},3784:function(t,e,r){"use strict";var n=r(4433);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},1995:function(t,e,r){"use strict";var n=r(3784),o=Math.min;t.exports=function(t){var e=n(t);return e>0?o(e,9007199254740991):0}},9452:function(t,e,r){"use strict";var n=r(1365),o=Object;t.exports=function(t){return o(n(t))}},7762:function(t,e,r){"use strict";var n=r(3736),o=r(5343),i=r(5648),a=r(4897),s=r(8367),u=r(1642),c=TypeError,l=u("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,u=a(t,l);if(u){if(void 0===e&&(e="default"),r=n(u,t,e),!o(r)||i(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(t,e)}},8:function(t,e,r){"use strict";var n=r(7762),o=r(5648);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},6390:function(t){"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},6177:function(t,e,r){"use strict";var n=r(7889),o=0,i=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++o+i,36)}},2865:function(t,e,r){"use strict";function n(t){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=r(6576);t.exports=o&&!Symbol.sham&&"symbol"==n(Symbol.iterator)},1363:function(t,e,r){"use strict";var n=r(4797),o=r(3634);t.exports=n&&o(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},6185:function(t,e,r){"use strict";var n=r(8029),o=r(5164),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},2860:function(t,e,r){"use strict";var n=r(7520),o=r(2770),i=r(8490),a=r(754).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});o(e,t)||a(e,t,{value:i.f(t)})}},8490:function(t,e,r){"use strict";var n=r(1642);e.f=n},1642:function(t,e,r){"use strict";var n=r(8029),o=r(1930),i=r(2770),a=r(6177),s=r(6576),u=r(2865),c=n.Symbol,l=o("wks"),f=u?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return i(l,t)||(l[t]=s&&i(c,t)?c[t]:f("Symbol."+t)),l[t]}},6186:function(t,e,r){"use strict";var n=r(3485),o=r(8192);n({target:"Object",stat:!0,arity:2,forced:Object.assign!==o},{assign:o})},9319:function(t,e,r){"use strict";r(2860)("asyncIterator")},3725:function(t,e,r){"use strict";var n=r(1606);t.exports=n},756:function(t,e,r){"use strict";var n=r(379);t.exports=n}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.loaded=!0,i.exports}r.amdO={},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t},r(5641),r(3416);r(4257)}();