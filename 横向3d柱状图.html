<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>横向3D柱状图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      #chart-container {
        width: 100%;
        height: 600px;
        background-color: #0a1d35;
        border-radius: 4px;
        padding: 15px;
        box-sizing: border-box;
      }
      body {
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        font-family: Arial, sans-serif;
      }
    </style>
  </head>
  <body>
    <div id="chart-container"></div>

    <script>
      // 注册自定义图形-横向3D柱状图
      function registerHorizontalShape() {
        const width = 12
        const depthOffset = 6
        const name = 'HorizontalCube'

        // 柱体前面
        const CubeFront = echarts.graphic.extendShape({
          shape: { x: 0, y: 0 },
          buildPath(ctx, shape) {
            const yAxisPoint = shape.yAxisPoint
            const c0 = [shape.x, shape.y]
            const c1 = [shape.x + depthOffset, shape.y - width]
            const c2 = [yAxisPoint[0] + depthOffset, yAxisPoint[1] - width]
            const c3 = [yAxisPoint[0], yAxisPoint[1]]
            ctx
              .moveTo(c0[0], c0[1])
              .lineTo(c1[0], c1[1])
              .lineTo(c2[0], c2[1])
              .lineTo(c3[0], c3[1])
              .closePath()
          },
        })

        // 柱体后面
        const CubeBack = echarts.graphic.extendShape({
          shape: { x: 0, y: 0 },
          buildPath(ctx, shape) {
            const yAxisPoint = shape.yAxisPoint
            const withOffset = shape.with || 0
            const offsetX = shape.offsetX || 0 // 新增：向右偏移量

            const c0 = [shape.x + offsetX, shape.y + withOffset]
            const c1 = [yAxisPoint[0] + offsetX, yAxisPoint[1] + withOffset]
            const c2 = [
              yAxisPoint[0] + offsetX + depthOffset,
              yAxisPoint[1] - width + withOffset,
            ]
            const c3 = [
              shape.x + offsetX + depthOffset,
              shape.y - width + withOffset,
            ]

            ctx
              .moveTo(c0[0], c0[1])
              .lineTo(c1[0], c1[1])
              .lineTo(c2[0], c2[1])
              .lineTo(c3[0], c3[1])
              .closePath()
          },
        })

        // 柱体顶面
        const CubeTop = echarts.graphic.extendShape({
          shape: { x: 0, y: 0 },
          buildPath(ctx, shape) {
            const c0 = [shape.x, shape.y]
            const c1 = [shape.x + depthOffset, shape.y - width]
            const c2 = [shape.x + 2 * depthOffset, shape.y]
            const c3 = [shape.x + depthOffset, shape.y + width]
            ctx
              .moveTo(c0[0], c0[1])
              .lineTo(c1[0], c1[1])
              .lineTo(c2[0], c2[1])
              .lineTo(c3[0], c3[1])
              .closePath()
          },
        })

        // 顶部白线 1
        const CubeTopLine1 = echarts.graphic.extendShape({
          shape: { x: 0, y: 0 },
          buildPath(ctx, shape) {
            const c0 = [shape.x, shape.y]
            const c1 = [shape.x + depthOffset, shape.y - width]
            ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1])
          },
        })

        // 顶部白线 2
        const CubeTopLine2 = echarts.graphic.extendShape({
          shape: { x: 0, y: 0 },
          buildPath(ctx, shape) {
            const c0 = [shape.x, shape.y]
            const c1 = [shape.x + depthOffset, shape.y + width]
            ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1])
          },
        })

        // 中间白线
        const CubeMiddleLine = echarts.graphic.extendShape({
          shape: { x: 0, y: 0, yAxisPoint: [0, 0] },
          buildPath(ctx, shape) {
            const yAxisPoint = shape.yAxisPoint
            const c0 = [shape.x, shape.y]
            const c1 = [yAxisPoint[0], shape.y]
            ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1])
          },
        })

        // 柱体底部菱形
        const CubeBottom = echarts.graphic.extendShape({
          shape: { x: 0, y: 0, yAxisPoint: [0, 0] },
          buildPath(ctx, shape) {
            const yAxisPoint = shape.yAxisPoint
            const c0 = [yAxisPoint[0], yAxisPoint[1]]
            const c1 = [yAxisPoint[0] + depthOffset, yAxisPoint[1] - width]
            const c2 = [yAxisPoint[0] + 2 * depthOffset, yAxisPoint[1]]
            const c3 = [yAxisPoint[0] + depthOffset, yAxisPoint[1] + width]
            ctx
              .moveTo(c0[0], c0[1])
              .lineTo(c1[0], c1[1])
              .lineTo(c2[0], c2[1])
              .lineTo(c3[0], c3[1])
              .closePath()
          },
        })

        // 统一注册
        echarts.graphic.registerShape(`${name}Front`, CubeFront)
        echarts.graphic.registerShape(`${name}Back`, CubeBack)
        echarts.graphic.registerShape(`${name}Top`, CubeTop)
        echarts.graphic.registerShape(`${name}Bottom`, CubeBottom)
        echarts.graphic.registerShape(`${name}TopLine1`, CubeTopLine1)
        echarts.graphic.registerShape(`${name}TopLine2`, CubeTopLine2)
        echarts.graphic.registerShape(`${name}MiddleLine`, CubeMiddleLine)
      }

      // 初始化图表
      function initChart() {
        const chartDom = document.getElementById('chart-container')
        const chartInstance = echarts.init(chartDom)

        // 数据
        const itemStatusData = [
          { category: '值机柜台', inUse: 45, disabled: 27, faulty: 28 },
          { category: '安检通道', inUse: 45, disabled: 27, faulty: 28 },
          { category: '登机口', inUse: 45, disabled: 27, faulty: 28 },
          { category: '行李转盘', inUse: 45, disabled: 27, faulty: 28 },
        ]

        const option = {
          grid: {
            left: '5%',
            right: '12%',
            bottom: '10%',
            top: '10%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
            borderColor: 'rgba(255, 255, 255, 0.3)',
            borderWidth: 0.88,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 12,
            },
            formatter(params) {
              const name = params[0].name
              let result = `<div style="min-width: 140px;padding: 5px">
                            <div style="font-size: 14px; font-weight: 400; color: #FFFFFF; margin-bottom: 8px; line-height: 1.2;">
                                ${name}
                            </div>`

              params.forEach((param) => {
                const color = param.color
                const value = param.value
                const seriesName = param.seriesName
                result += `<div style="display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                <span style="width: 12px; height: 12px; background-color: ${color}; display: inline-block;"></span>
                                <span style="color: rgba(255, 255, 255, 0.6); font-size: 14px;">${seriesName}</span>
                                <span style="color: #FFFFFF; font-size: 14px; font-weight: 500; margin-left: auto;">${value}</span>
                            </div>`
              })

              result += '</div>'
              return result
            },
            extraCssText:
              'box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6); border-radius: 0px; backdrop-filter: blur(10px);',
          },
          legend: {
            show: true,
            top: 0,
            right: 0,
            itemWidth: 12,
            itemHeight: 12,
            formatter: (name) => {
              if (name === '占有') return `{a|} {name|${name}}`
              if (name === '空闲') return `{b|} {name|${name}}`
              if (name === '故障') return `{c|} {name|${name}}`
            },
            textStyle: {
              color: 'rgba(255, 255, 255, 0.74)',
              fontSize: 12,
              rich: {
                a: {
                  width: 12,
                  height: 12,
                  backgroundColor: 'rgba(26, 141, 255, 1)',
                },
                b: {
                  width: 12,
                  height: 12,
                  backgroundColor: 'rgba(66, 207, 149, 1)',
                },
                c: {
                  width: 12,
                  height: 12,
                  backgroundColor: 'rgba(255, 255, 26, 1)',
                },
                name: {
                  color: 'rgba(255, 255, 255, 0.74)',
                  fontSize: 12,
                  fontWeight: '400',
                  padding: [2, 0, 0, 0],
                },
              },
            },
            icon: 'none',
            data: ['故障', '空闲', '占有'],
          },
          yAxis: {
            type: 'category',
            data: itemStatusData.map((item) => item.category),
            axisLine: {
              show: true,
              lineStyle: { color: 'rgba(108, 128, 151, 1)' },
            },
            axisTick: { show: false },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(255, 255, 255, 0.6)',
            },
          },
          xAxis: {
            type: 'value',
            min: 0,
            max: 100,
            axisLine: { show: false },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)',
                type: 'dashed',
                width: 1,
              },
            },
            axisTick: { show: false },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(108, 128, 151, 1)',
            },
          },
          series: [
            // 占有（左侧）
            {
              name: '占有',
              type: 'custom',
              data: itemStatusData.map((item, index) => [item.inUse, index]),
              stack: 'total',
              animationDelay(idx) {
                return idx * 120
              },
              animationDuration: 800,
              animationEasing: 'cubicOut',
              renderItem: (params, api) => {
                const location = api.coord([api.value(0), api.value(1)])
                const yAxisPoint = api.coord([0, api.value(1)])

                return {
                  type: 'group',
                  enterFrom: { scaleX: 0.01 },
                  enterAnimation: { duration: 800, easing: 'cubicOut' },
                  updateAnimation: { duration: 800, easing: 'cubicOut' },
                  originX: yAxisPoint[0],
                  children: [
                    {
                      type: 'HorizontalCubeFront',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [yAxisPoint[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(0, 114, 221, 1)' },
                          { offset: 1, color: 'rgba(0, 148, 255, 0.2)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeBack',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [yAxisPoint[0], yAxisPoint[1]],
                        with: 12,
                        offsetX: 6, // 向右平移 6 像素
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(0, 114, 221, 1)' },
                          { offset: 1, color: 'rgba(0, 148, 255, 0.2)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeTop',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        fill: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                          { offset: 0, color: 'rgba(0, 114, 221, 1)' },
                          { offset: 1, color: 'rgba(129, 228, 255, 1)' },
                        ]),
                      },
                    },

                    {
                      type: 'HorizontalCubeBottom',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [yAxisPoint[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(0, 114, 221, 1)' },
                          { offset: 1, color: 'rgba(0, 148, 255, 0.2)' },
                        ]),
                        stroke: '#134E9F',
                        lineWidth: 1,
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine1',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine2',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeMiddleLine',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [yAxisPoint[0], yAxisPoint[1]],
                      },
                      style: {
                        lineWidth: 1,
                        stroke: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: '#FFFFFF' },
                          { offset: 1, color: 'rgba(12, 133, 189, 0.2)' },
                        ]),
                      },
                    },
                  ],
                }
              },
            },
            // 空闲（中间）
            {
              name: '空闲',
              type: 'custom',
              data: itemStatusData.map((item, index) => [item.disabled, index]),
              stack: 'total',
              animationDelay(idx) {
                return idx * 120 + 100
              },
              animationDuration: 800,
              animationEasing: 'cubicOut',
              renderItem: (params, api) => {
                const yIndex = api.value(1)
                const yAxisPoint = api.coord([0, yIndex])
                const inUseValue = itemStatusData[yIndex].inUse
                const inUseLocation = api.coord([inUseValue, yIndex])
                const location = api.coord([inUseValue + api.value(0), yIndex])

                return {
                  type: 'group',
                  enterFrom: { scaleX: 0.01 },
                  enterAnimation: { duration: 800, easing: 'cubicOut' },
                  updateAnimation: { duration: 800, easing: 'cubicOut' },
                  originX: inUseLocation[0],
                  children: [
                    {
                      type: 'HorizontalCubeFront',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [inUseLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(53, 205, 148, 1)' },
                          { offset: 1, color: 'rgba(53, 205, 148, 0.2)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeBack',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [inUseLocation[0], yAxisPoint[1]],
                        with: 12,
                        offsetX: 6, // 向右平移 6 像素
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(53, 205, 148, 1)' },
                          { offset: 1, color: 'rgba(53, 205, 148, 0.2)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeTop',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        fill: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                          { offset: 0, color: 'rgba(53, 205, 148, 1)' },
                          { offset: 1, color: 'rgba(190, 255, 220, 1)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeBottom',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [inUseLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(53, 205, 148, 1)' },
                          { offset: 1, color: 'rgba(53, 205, 148, 0.2)' },
                        ]),
                        stroke: '#1E8F6B',
                        lineWidth: 1,
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine1',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine2',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeMiddleLine',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [inUseLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        lineWidth: 1,
                        stroke: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                          { offset: 0, color: '#FFFFFF' },
                          { offset: 1, color: 'rgba(12, 133, 189, 0.2)' },
                        ]),
                      },
                    },
                  ],
                }
              },
            },
            // 故障（右侧）
            {
              name: '故障',
              type: 'custom',
              data: itemStatusData.map((item, index) => [item.faulty, index]),
              stack: 'total',
              animationDelay(idx) {
                return idx * 120 + 200
              },
              animationDuration: 800,
              animationEasing: 'cubicOut',
              renderItem: (params, api) => {
                const yIndex = api.value(1)
                const yAxisPoint = api.coord([0, yIndex])
                const inUseValue = itemStatusData[yIndex].inUse
                const disabledValue = itemStatusData[yIndex].disabled
                const startLocation = api.coord([
                  inUseValue + disabledValue,
                  yIndex,
                ])
                const location = api.coord([
                  inUseValue + disabledValue + api.value(0),
                  yIndex,
                ])

                return {
                  type: 'group',
                  enterFrom: { scaleX: 0.01 },
                  enterAnimation: { duration: 800, easing: 'cubicOut' },
                  updateAnimation: { duration: 800, easing: 'cubicOut' },
                  originX: startLocation[0],
                  children: [
                    {
                      type: 'HorizontalCubeFront',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [startLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(255, 216, 60, 1)' },
                          { offset: 1, color: 'rgba(255, 216, 60, 0.25)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeBack',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [startLocation[0], yAxisPoint[1]],
                        with: 12,
                        offsetX: 6, // 向右平移 6 像素
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(255, 216, 60, 1)' },
                          { offset: 1, color: 'rgba(255, 216, 60, 0.25)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeTop',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        fill: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                          { offset: 0, color: 'rgba(255, 216, 60, 1)' },
                          { offset: 1, color: 'rgba(255, 255, 180, 1)' },
                        ]),
                      },
                    },
                    {
                      type: 'HorizontalCubeBottom',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [startLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        fill: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                          { offset: 0, color: 'rgba(255, 216, 60, 1)' },
                          { offset: 1, color: 'rgba(255, 216, 60, 0.25)' },
                        ]),
                        stroke: '#CFA21B',
                        lineWidth: 1,
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine1',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeTopLine2',
                      shape: { x: location[0], y: location[1] },
                      style: {
                        lineWidth: 1,
                        stroke: 'rgba(255, 255, 255, 0.8)',
                      },
                    },
                    {
                      type: 'HorizontalCubeMiddleLine',
                      shape: {
                        x: location[0],
                        y: location[1],
                        yAxisPoint: [startLocation[0], yAxisPoint[1]],
                      },
                      style: {
                        lineWidth: 1,
                        stroke: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                          { offset: 0, color: '#FFFFFF' },
                          { offset: 1, color: 'rgba(12, 133, 189, 0.2)' },
                        ]),
                      },
                    },
                  ],
                }
              },
            },
          ],
        }

        chartInstance.setOption(option)

        // 响应窗口调整
        window.addEventListener('resize', function () {
          chartInstance.resize()
        })
      }

      // 注册自定义形状并初始化图表
      registerHorizontalShape()
      initChart()
    </script>
  </body>
</html>
