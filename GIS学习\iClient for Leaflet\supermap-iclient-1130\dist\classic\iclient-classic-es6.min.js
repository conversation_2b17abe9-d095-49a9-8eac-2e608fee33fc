(()=>{var t={444:function(t){!function(e){"use strict";if(e.__disableNativeFetch||!e.fetch){a.prototype.append=function(t,e){t=i(t),e=o(e);var r=this.map[t];r||(r=[],this.map[t]=r),r.push(e)},a.prototype.delete=function(t){delete this.map[i(t)]},a.prototype.get=function(t){var e=this.map[i(t)];return e?e[0]:null},a.prototype.getAll=function(t){return this.map[i(t)]||[]},a.prototype.has=function(t){return this.map.hasOwnProperty(i(t))},a.prototype.set=function(t,e){this.map[i(t)]=[o(e)]},a.prototype.forEach=function(t,e){Object.getOwnPropertyNames(this.map).forEach(function(r){this.map[r].forEach(function(s){t.call(e,s,r,this)},this)},this)};var r={blob:"FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(t){return!1}}(),formData:"FormData"in e,arrayBuffer:"ArrayBuffer"in e},s=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];p.prototype.clone=function(){return new p(this)},c.call(p.prototype),c.call(m.prototype),m.prototype.clone=function(){return new m(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new a(this.headers),url:this.url})},m.error=function(){var t=new m(null,{status:0,statusText:""});return t.type="error",t};var n=[301,302,303,307,308];m.redirect=function(t,e){if(-1===n.indexOf(e))throw new RangeError("Invalid status code");return new m(null,{status:e,headers:{location:t}})},e.Headers=a,e.Request=p,e.Response=m,e.fetch=function(t,e){return new Promise(function(s,n){var i;i=p.prototype.isPrototypeOf(t)&&!e?t:new p(t,e);var o=new XMLHttpRequest;var u=!1;function l(){if(4===o.readyState){var t=1223===o.status?204:o.status;if(t<100||t>599){if(u)return;return u=!0,void n(new TypeError("Network request failed"))}var e={status:t,statusText:o.statusText,headers:function(t){var e=new a;return t.getAllResponseHeaders().trim().split("\n").forEach(function(t){var r=t.trim().split(":"),s=r.shift().trim(),n=r.join(":").trim();e.append(s,n)}),e}(o),url:"responseURL"in o?o.responseURL:/^X-Request-URL:/m.test(o.getAllResponseHeaders())?o.getResponseHeader("X-Request-URL"):void 0},r="response"in o?o.response:o.responseText;u||(u=!0,s(new m(r,e)))}}o.onreadystatechange=l,o.onload=l,o.onerror=function(){u||(u=!0,n(new TypeError("Network request failed")))},o.open(i.method,i.url,!0);try{"include"===i.credentials&&("withCredentials"in o?o.withCredentials=!0:console&&console.warn&&console.warn("withCredentials is not supported, you can ignore this warning"))}catch(t){console&&console.warn&&console.warn("set withCredentials error:"+t)}"responseType"in o&&r.blob&&(o.responseType="blob"),i.headers.forEach(function(t,e){o.setRequestHeader(e,t)}),o.send(void 0===i._bodyInit?null:i._bodyInit)})},e.fetch.polyfill=!0,t.exports&&(t.exports=e.fetch)}function i(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(t))throw new TypeError("Invalid character in header field name");return t.toLowerCase()}function o(t){return"string"!=typeof t&&(t=String(t)),t}function a(t){this.map={},t instanceof a?t.forEach(function(t,e){this.append(e,t)},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function u(t){if(t.bodyUsed)return Promise.reject(new TypeError("Already read"));t.bodyUsed=!0}function l(t){return new Promise(function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}})}function h(t){var e=new FileReader;return e.readAsArrayBuffer(t),l(e)}function c(){return this.bodyUsed=!1,this._initBody=function(t,e){if(this._bodyInit=t,"string"==typeof t)this._bodyText=t;else if(r.blob&&Blob.prototype.isPrototypeOf(t))this._bodyBlob=t,this._options=e;else if(r.formData&&FormData.prototype.isPrototypeOf(t))this._bodyFormData=t;else if(t){if(!r.arrayBuffer||!ArrayBuffer.prototype.isPrototypeOf(t))throw new Error("unsupported BodyInit type")}else this._bodyText=""},r.blob?(this.blob=function(){var t=u(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this.blob().then(h)},this.text=function(){var t,e,r,s,n,i,o,a=u(this);if(a)return a;if(this._bodyBlob)return t=this._bodyBlob,e=this._options,r=new FileReader,s=e.headers.map["content-type"]?e.headers.map["content-type"].toString():"",n=/charset\=[0-9a-zA-Z\-\_]*;?/,i=t.type.match(n)||s.match(n),o=[t],i&&o.push(i[0].replace(/^charset\=/,"").replace(/;$/,"")),r.readAsText.apply(r,o),l(r);if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)}):this.text=function(){var t=u(this);return t||Promise.resolve(this._bodyText)},r.formData&&(this.formData=function(){return this.text().then(d)}),this.json=function(){return this.text().then(JSON.parse)},this}function p(t,e){var r,n,i=(e=e||{}).body;if(p.prototype.isPrototypeOf(t)){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new a(t.headers)),this.method=t.method,this.mode=t.mode,i||(i=t._bodyInit,t.bodyUsed=!0)}else this.url=t;if(this.credentials=e.credentials||this.credentials||"omit",!e.headers&&this.headers||(this.headers=new a(e.headers)),this.method=(r=e.method||this.method||"GET",n=r.toUpperCase(),s.indexOf(n)>-1?n:r),this.mode=e.mode||this.mode||null,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(i,e)}function d(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var r=t.split("="),s=r.shift().replace(/\+/g," "),n=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(s),decodeURIComponent(n))}}),e}function m(t,e){e||(e={}),this._initBody(t,e),this.type="default",this.status=e.status,this.ok=this.status>=200&&this.status<300,this.statusText=e.statusText,this.headers=e.headers instanceof a?e.headers:new a(e.headers),this.url=e.url||""}}("undefined"!=typeof self?self:this)},348:function(t,e){var r,s,n;s=[e,t],void 0===(n="function"==typeof(r=function(t,e){"use strict";var r={timeout:5e3,jsonpCallback:"callback",jsonpCallbackFunction:null};function s(t){try{delete window[t]}catch(e){window[t]=void 0}}function n(t){var e=document.getElementById(t);e&&document.getElementsByTagName("head")[0].removeChild(e)}e.exports=function(t){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],i=t,o=e.timeout||r.timeout,a=e.jsonpCallback||r.jsonpCallback,u=void 0;return new Promise(function(r,l){var h=e.jsonpCallbackFunction||"jsonp_"+Date.now()+"_"+Math.ceil(1e5*Math.random()),c=a+"_"+h;window[h]=function(t){r({ok:!0,json:function(){return Promise.resolve(t)}}),u&&clearTimeout(u),n(c),s(h)},i+=-1===i.indexOf("?")?"?":"&";var p=document.createElement("script");p.setAttribute("src",""+i+a+"="+h),e.charset&&p.setAttribute("charset",e.charset),p.id=c,document.getElementsByTagName("head")[0].appendChild(p),u=setTimeout(function(){l(new Error("JSONP request to "+t+" timed out")),s(h),n(c),window[h]=function(){s(h)}},o),p.onerror=function(){l(new Error("JSONP request to "+t+" failed")),s(h),n(c),u&&clearTimeout(u)}})}})?r.apply(e,s):r)||(t.exports=n)},836:function(t,e,r){!function(){"use strict";function t(t){var e=this.constructor;return this.then(function(r){return e.resolve(t()).then(function(){return r})},function(r){return e.resolve(t()).then(function(){return e.reject(r)})})}function e(t){return new this(function(e,r){if(!t||void 0===t.length)return r(new TypeError(typeof t+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var s=Array.prototype.slice.call(t);if(0===s.length)return e([]);var n=s.length;function i(t,r){if(r&&("object"==typeof r||"function"==typeof r)){var o=r.then;if("function"==typeof o)return void o.call(r,function(e){i(t,e)},function(r){s[t]={status:"rejected",reason:r},0==--n&&e(s)})}s[t]={status:"fulfilled",value:r},0==--n&&e(s)}for(var o=0;o<s.length;o++)i(o,s[o])})}var s=setTimeout;function n(t){return Boolean(t&&void 0!==t.length)}function i(){}function o(t){if(!(this instanceof o))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],c(t,this)}function a(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,o._immediateFn(function(){var r=1===t._state?e.onFulfilled:e.onRejected;if(null!==r){var s;try{s=r(t._value)}catch(t){return void l(e.promise,t)}u(e.promise,s)}else(1===t._state?u:l)(e.promise,t._value)})):t._deferreds.push(e)}function u(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var r=e.then;if(e instanceof o)return t._state=3,t._value=e,void h(t);if("function"==typeof r)return void c((s=r,n=e,function(){s.apply(n,arguments)}),t)}t._state=1,t._value=e,h(t)}catch(e){l(t,e)}var s,n}function l(t,e){t._state=2,t._value=e,h(t)}function h(t){2===t._state&&0===t._deferreds.length&&o._immediateFn(function(){t._handled||o._unhandledRejectionFn(t._value)});for(var e=0,r=t._deferreds.length;e<r;e++)a(t,t._deferreds[e]);t._deferreds=null}function c(t,e){var r=!1;try{t(function(t){r||(r=!0,u(e,t))},function(t){r||(r=!0,l(e,t))})}catch(t){if(r)return;r=!0,l(e,t)}}o.prototype.catch=function(t){return this.then(null,t)},o.prototype.then=function(t,e){var r=new this.constructor(i);return a(this,new function(t,e,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=r}(t,e,r)),r},o.prototype.finally=t,o.all=function(t){return new o(function(e,r){if(!n(t))return r(new TypeError("Promise.all accepts an array"));var s=Array.prototype.slice.call(t);if(0===s.length)return e([]);var i=s.length;function o(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var a=n.then;if("function"==typeof a)return void a.call(n,function(e){o(t,e)},r)}s[t]=n,0==--i&&e(s)}catch(t){r(t)}}for(var a=0;a<s.length;a++)o(a,s[a])})},o.allSettled=e,o.resolve=function(t){return t&&"object"==typeof t&&t.constructor===o?t:new o(function(e){e(t)})},o.reject=function(t){return new o(function(e,r){r(t)})},o.race=function(t){return new o(function(e,r){if(!n(t))return r(new TypeError("Promise.race accepts an array"));for(var s=0,i=t.length;s<i;s++)o.resolve(t[s]).then(e,r)})},o._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){s(t,0)},o._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var p=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}();"function"!=typeof p.Promise?p.Promise=o:(p.Promise.prototype.finally||(p.Promise.prototype.finally=t),p.Promise.allSettled||(p.Promise.allSettled=e))}()},430:function(t,e,r){var s,n;!function(i,o){"use strict";t.exports?t.exports=o():void 0===(n="function"==typeof(s=o)?s.call(e,r,e,t):s)||(t.exports=n)}(0,function(t){"use strict";var e=t&&t.IPv6;return{best:function(t){var e,r,s=t.toLowerCase().split(":"),n=s.length,i=8;for(""===s[0]&&""===s[1]&&""===s[2]?(s.shift(),s.shift()):""===s[0]&&""===s[1]?s.shift():""===s[n-1]&&""===s[n-2]&&s.pop(),-1!==s[(n=s.length)-1].indexOf(".")&&(i=7),e=0;e<n&&""!==s[e];e++);if(e<i)for(s.splice(e,1,"0000");s.length<i;)s.splice(e,0,"0000");for(var o=0;o<i;o++){r=s[o].split("");for(var a=0;a<3&&"0"===r[0]&&r.length>1;a++)r.splice(0,1);s[o]=r.join("")}var u=-1,l=0,h=0,c=-1,p=!1;for(o=0;o<i;o++)p?"0"===s[o]?h+=1:(p=!1,h>l&&(u=c,l=h)):"0"===s[o]&&(p=!0,c=o,h=1);h>l&&(u=c,l=h),l>1&&s.splice(u,l,""),n=s.length;var d="";for(""===s[0]&&(d=":"),o=0;o<n&&(d+=s[o],o!==n-1);o++)d+=":";return""===s[n-1]&&(d+=":"),d},noConflict:function(){return t.IPv6===this&&(t.IPv6=e),this}}})},704:function(t,e,r){var s,n;!function(i,o){"use strict";t.exports?t.exports=o():void 0===(n="function"==typeof(s=o)?s.call(e,r,e,t):s)||(t.exports=n)}(0,function(t){"use strict";var e=t&&t.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;var s=t.lastIndexOf(".",e-1);if(s<=0||s>=e-1)return!1;var n=r.list[t.slice(e+1)];return!!n&&n.indexOf(" "+t.slice(s+1,e)+" ")>=0},is:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;if(t.lastIndexOf(".",e-1)>=0)return!1;var s=r.list[t.slice(e+1)];return!!s&&s.indexOf(" "+t.slice(0,e)+" ")>=0},get:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return null;var s=t.lastIndexOf(".",e-1);if(s<=0||s>=e-1)return null;var n=r.list[t.slice(e+1)];return n?n.indexOf(" "+t.slice(s+1,e)+" ")<0?null:t.slice(s+1):null},noConflict:function(){return t.SecondLevelDomains===this&&(t.SecondLevelDomains=e),this}};return r})},193:function(t,e,r){var s,n,i;!function(o,a){"use strict";t.exports?t.exports=a(r(340),r(430),r(704)):(n=[r(340),r(430),r(704)],void 0===(i="function"==typeof(s=a)?s.apply(e,n):s)||(t.exports=i))}(0,function(t,e,r,s){"use strict";var n=s&&s.URI;function i(t,e){var r=arguments.length>=1,s=arguments.length>=2;if(!(this instanceof i))return r?s?new i(t,e):new i(t):new i;if(void 0===t){if(r)throw new TypeError("undefined is not a valid argument for URI");t="undefined"!=typeof location?location.href+"":""}if(null===t&&r)throw new TypeError("null is not a valid argument for URI");return this.href(t),void 0!==e?this.absoluteTo(e):this}i.version="1.19.11";var o=i.prototype,a=Object.prototype.hasOwnProperty;function u(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(t){return void 0===t?"Undefined":String(Object.prototype.toString.call(t)).slice(8,-1)}function h(t){return"Array"===l(t)}function c(t,e){var r,s,n={};if("RegExp"===l(e))n=null;else if(h(e))for(r=0,s=e.length;r<s;r++)n[e[r]]=!0;else n[e]=!0;for(r=0,s=t.length;r<s;r++){(n&&void 0!==n[t[r]]||!n&&e.test(t[r]))&&(t.splice(r,1),s--,r--)}return t}function p(t,e){var r,s;if(h(e)){for(r=0,s=e.length;r<s;r++)if(!p(t,e[r]))return!1;return!0}var n=l(e);for(r=0,s=t.length;r<s;r++)if("RegExp"===n){if("string"==typeof t[r]&&t[r].match(e))return!0}else if(t[r]===e)return!0;return!1}function d(t,e){if(!h(t)||!h(e))return!1;if(t.length!==e.length)return!1;t.sort(),e.sort();for(var r=0,s=t.length;r<s;r++)if(t[r]!==e[r])return!1;return!0}function m(t){return t.replace(/^\/+|\/+$/g,"")}function f(t){return escape(t)}function g(t){return encodeURIComponent(t).replace(/[!'()*]/g,f).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(t){if(t&&t.nodeName){var e=t.nodeName.toLowerCase();if("input"!==e||"image"===t.type)return i.domAttributes[e]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(t,e){var r=i.encode(t+"");return void 0===e&&(e=i.escapeQuerySpace),e?r.replace(/%20/g,"+"):r},i.decodeQuery=function(t,e){t+="",void 0===e&&(e=i.escapeQuerySpace);try{return i.decode(e?t.replace(/\+/g,"%20"):t)}catch(e){return t}};var y,v={encode:"encode",decode:"decode"},b=function(t,e){return function(r){try{return i[e](r+"").replace(i.characters[t][e].expression,function(r){return i.characters[t][e].map[r]})}catch(t){return r}}};for(y in v)i[y+"PathSegment"]=b("pathname",v[y]),i[y+"UrnPathSegment"]=b("urnpath",v[y]);var S=function(t,e,r){return function(s){var n;n=r?function(t){return i[e](i[r](t))}:i[e];for(var o=(s+"").split(t),a=0,u=o.length;a<u;a++)o[a]=n(o[a]);return o.join(t)}};function _(t){return function(e,r){return void 0===e?this._parts[t]||"":(this._parts[t]=e||null,this.build(!r),this)}}function w(t,e){return function(r,s){return void 0===r?this._parts[t]||"":(null!==r&&(r+="").charAt(0)===e&&(r=r.substring(1)),this._parts[t]=r,this.build(!s),this)}}i.decodePath=S("/","decodePathSegment"),i.decodeUrnPath=S(":","decodeUrnPathSegment"),i.recodePath=S("/","encodePathSegment","decode"),i.recodeUrnPath=S(":","encodeUrnPathSegment","decode"),i.encodeReserved=b("reserved","encode"),i.parse=function(t,e){var r;return e||(e={preventInvalidHostname:i.preventInvalidHostname}),(r=(t=(t=t.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(e.fragment=t.substring(r+1)||null,t=t.substring(0,r)),(r=t.indexOf("?"))>-1&&(e.query=t.substring(r+1)||null,t=t.substring(0,r)),"//"===(t=(t=t.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(e.protocol=null,t=t.substring(2),t=i.parseAuthority(t,e)):(r=t.indexOf(":"))>-1&&(e.protocol=t.substring(0,r)||null,e.protocol&&!e.protocol.match(i.protocol_expression)?e.protocol=void 0:"//"===t.substring(r+1,r+3).replace(/\\/g,"/")?(t=t.substring(r+3),t=i.parseAuthority(t,e)):(t=t.substring(r+1),e.urn=!0)),e.path=t,e},i.parseHost=function(t,e){t||(t="");var r,s,n=(t=t.replace(/\\/g,"/")).indexOf("/");if(-1===n&&(n=t.length),"["===t.charAt(0))r=t.indexOf("]"),e.hostname=t.substring(1,r)||null,e.port=t.substring(r+2,n)||null,"/"===e.port&&(e.port=null);else{var o=t.indexOf(":"),a=t.indexOf("/"),u=t.indexOf(":",o+1);-1!==u&&(-1===a||u<a)?(e.hostname=t.substring(0,n)||null,e.port=null):(s=t.substring(0,n).split(":"),e.hostname=s[0]||null,e.port=s[1]||null)}return e.hostname&&"/"!==t.substring(n).charAt(0)&&(n++,t="/"+t),e.preventInvalidHostname&&i.ensureValidHostname(e.hostname,e.protocol),e.port&&i.ensureValidPort(e.port),t.substring(n)||"/"},i.parseAuthority=function(t,e){return t=i.parseUserinfo(t,e),i.parseHost(t,e)},i.parseUserinfo=function(t,e){var r=t;-1!==t.indexOf("\\")&&(t=t.replace(/\\/g,"/"));var s,n=t.indexOf("/"),o=t.lastIndexOf("@",n>-1?n:t.length-1);return o>-1&&(-1===n||o<n)?(s=t.substring(0,o).split(":"),e.username=s[0]?i.decode(s[0]):null,s.shift(),e.password=s[0]?i.decode(s.join(":")):null,t=r.substring(o+1)):(e.username=null,e.password=null),t},i.parseQuery=function(t,e){if(!t)return{};if(!(t=t.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,s,n,o={},u=t.split("&"),l=u.length,h=0;h<l;h++)r=u[h].split("="),s=i.decodeQuery(r.shift(),e),n=r.length?i.decodeQuery(r.join("="),e):null,"__proto__"!==s&&(a.call(o,s)?("string"!=typeof o[s]&&null!==o[s]||(o[s]=[o[s]]),o[s].push(n)):o[s]=n);return o},i.build=function(t){var e="",r=!1;return t.protocol&&(e+=t.protocol+":"),t.urn||!e&&!t.hostname||(e+="//",r=!0),e+=i.buildAuthority(t)||"","string"==typeof t.path&&("/"!==t.path.charAt(0)&&r&&(e+="/"),e+=t.path),"string"==typeof t.query&&t.query&&(e+="?"+t.query),"string"==typeof t.fragment&&t.fragment&&(e+="#"+t.fragment),e},i.buildHost=function(t){var e="";return t.hostname?(i.ip6_expression.test(t.hostname)?e+="["+t.hostname+"]":e+=t.hostname,t.port&&(e+=":"+t.port),e):""},i.buildAuthority=function(t){return i.buildUserinfo(t)+i.buildHost(t)},i.buildUserinfo=function(t){var e="";return t.username&&(e+=i.encode(t.username)),t.password&&(e+=":"+i.encode(t.password)),e&&(e+="@"),e},i.buildQuery=function(t,e,r){var s,n,o,u,l="";for(n in t)if("__proto__"!==n&&a.call(t,n))if(h(t[n]))for(s={},o=0,u=t[n].length;o<u;o++)void 0!==t[n][o]&&void 0===s[t[n][o]+""]&&(l+="&"+i.buildQueryParameter(n,t[n][o],r),!0!==e&&(s[t[n][o]+""]=!0));else void 0!==t[n]&&(l+="&"+i.buildQueryParameter(n,t[n],r));return l.substring(1)},i.buildQueryParameter=function(t,e,r){return i.encodeQuery(t,r)+(null!==e?"="+i.encodeQuery(e,r):"")},i.addQuery=function(t,e,r){if("object"==typeof e)for(var s in e)a.call(e,s)&&i.addQuery(t,s,e[s]);else{if("string"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===t[e])return void(t[e]=r);"string"==typeof t[e]&&(t[e]=[t[e]]),h(r)||(r=[r]),t[e]=(t[e]||[]).concat(r)}},i.setQuery=function(t,e,r){if("object"==typeof e)for(var s in e)a.call(e,s)&&i.setQuery(t,s,e[s]);else{if("string"!=typeof e)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");t[e]=void 0===r?null:r}},i.removeQuery=function(t,e,r){var s,n,o;if(h(e))for(s=0,n=e.length;s<n;s++)t[e[s]]=void 0;else if("RegExp"===l(e))for(o in t)e.test(o)&&(t[o]=void 0);else if("object"==typeof e)for(o in e)a.call(e,o)&&i.removeQuery(t,o,e[o]);else{if("string"!=typeof e)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===l(r)?!h(t[e])&&r.test(t[e])?t[e]=void 0:t[e]=c(t[e],r):t[e]!==String(r)||h(r)&&1!==r.length?h(t[e])&&(t[e]=c(t[e],r)):t[e]=void 0:t[e]=void 0}},i.hasQuery=function(t,e,r,s){switch(l(e)){case"String":break;case"RegExp":for(var n in t)if(a.call(t,n)&&e.test(n)&&(void 0===r||i.hasQuery(t,n,r)))return!0;return!1;case"Object":for(var o in e)if(a.call(e,o)&&!i.hasQuery(t,o,e[o]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(r)){case"Undefined":return e in t;case"Boolean":return r===Boolean(h(t[e])?t[e].length:t[e]);case"Function":return!!r(t[e],e,t);case"Array":return!!h(t[e])&&(s?p:d)(t[e],r);case"RegExp":return h(t[e])?!!s&&p(t[e],r):Boolean(t[e]&&t[e].match(r));case"Number":r=String(r);case"String":return h(t[e])?!!s&&p(t[e],r):t[e]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var t=[],e=[],r=0,s=0;s<arguments.length;s++){var n=new i(arguments[s]);t.push(n);for(var o=n.segment(),a=0;a<o.length;a++)"string"==typeof o[a]&&e.push(o[a]),o[a]&&r++}if(!e.length||!r)return new i("");var u=new i("").segment(e);return""!==t[0].path()&&"/"!==t[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(t,e){var r,s=Math.min(t.length,e.length);for(r=0;r<s;r++)if(t.charAt(r)!==e.charAt(r)){r--;break}return r<1?t.charAt(0)===e.charAt(0)&&"/"===t.charAt(0)?"/":"":("/"===t.charAt(r)&&"/"===e.charAt(r)||(r=t.substring(0,r).lastIndexOf("/")),t.substring(0,r+1))},i.withinString=function(t,e,r){r||(r={});var s=r.start||i.findUri.start,n=r.end||i.findUri.end,o=r.trim||i.findUri.trim,a=r.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(s.lastIndex=0;;){var l=s.exec(t);if(!l)break;var h=l.index;if(r.ignoreHtml){var c=t.slice(Math.max(h-3,0),h);if(c&&u.test(c))continue}for(var p=h+t.slice(h).search(n),d=t.slice(h,p),m=-1;;){var f=a.exec(d);if(!f)break;var g=f.index+f[0].length;m=Math.max(m,g)}if(!((d=m>-1?d.slice(0,m)+d.slice(m).replace(o,""):d.replace(o,"")).length<=l[0].length||r.ignore&&r.ignore.test(d))){var y=e(d,h,p=h+d.length,t);void 0!==y?(y=String(y),t=t.slice(0,h)+y+t.slice(p),s.lastIndex=h+y.length):s.lastIndex=p}}return s.lastIndex=0,t},i.ensureValidHostname=function(e,r){var s=!!e,n=!1;if(!!r&&(n=p(i.hostProtocols,r)),n&&!s)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(e&&e.match(i.invalid_hostname_characters)){if(!t)throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(t.toASCII(e).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(t){if(t){var e=Number(t);if(!(/^[0-9]+$/.test(e)&&e>0&&e<65536))throw new TypeError('Port "'+t+'" is not a valid port')}},i.noConflict=function(t){if(t){var e={URI:this.noConflict()};return s.URITemplate&&"function"==typeof s.URITemplate.noConflict&&(e.URITemplate=s.URITemplate.noConflict()),s.IPv6&&"function"==typeof s.IPv6.noConflict&&(e.IPv6=s.IPv6.noConflict()),s.SecondLevelDomains&&"function"==typeof s.SecondLevelDomains.noConflict&&(e.SecondLevelDomains=s.SecondLevelDomains.noConflict()),e}return s.URI===this&&(s.URI=n),this},o.build=function(t){return!0===t?this._deferred_build=!0:(void 0===t||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},o.clone=function(){return new i(this)},o.valueOf=o.toString=function(){return this.build(!1)._string},o.protocol=_("protocol"),o.username=_("username"),o.password=_("password"),o.hostname=_("hostname"),o.port=_("port"),o.query=w("query","?"),o.fragment=w("fragment","#"),o.search=function(t,e){var r=this.query(t,e);return"string"==typeof r&&r.length?"?"+r:r},o.hash=function(t,e){var r=this.fragment(t,e);return"string"==typeof r&&r.length?"#"+r:r},o.pathname=function(t,e){if(void 0===t||!0===t){var r=this._parts.path||(this._parts.hostname?"/":"");return t?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=t?i.recodeUrnPath(t):"":this._parts.path=t?i.recodePath(t):"/",this.build(!e),this},o.path=o.pathname,o.href=function(t,e){var r;if(void 0===t)return this.toString();this._string="",this._parts=i._parts();var s=t instanceof i,n="object"==typeof t&&(t.hostname||t.path||t.pathname);t.nodeName&&(t=t[i.getDomAttribute(t)]||"",n=!1);if(!s&&n&&void 0!==t.pathname&&(t=t.toString()),"string"==typeof t||t instanceof String)this._parts=i.parse(String(t),this._parts);else{if(!s&&!n)throw new TypeError("invalid input");var o=s?t._parts:t;for(r in o)"query"!==r&&a.call(this._parts,r)&&(this._parts[r]=o[r]);o.query&&this.query(o.query,!1)}return this.build(!e),this},o.is=function(t){var e=!1,s=!1,n=!1,o=!1,a=!1,u=!1,l=!1,h=!this._parts.urn;switch(this._parts.hostname&&(h=!1,s=i.ip4_expression.test(this._parts.hostname),n=i.ip6_expression.test(this._parts.hostname),a=(o=!(e=s||n))&&r&&r.has(this._parts.hostname),u=o&&i.idn_expression.test(this._parts.hostname),l=o&&i.punycode_expression.test(this._parts.hostname)),t.toLowerCase()){case"relative":return h;case"absolute":return!h;case"domain":case"name":return o;case"sld":return a;case"ip":return e;case"ip4":case"ipv4":case"inet4":return s;case"ip6":case"ipv6":case"inet6":return n;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return l}return null};var E=o.protocol,x=o.port,C=o.hostname;o.protocol=function(t,e){if(t&&!(t=t.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+t+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return E.call(this,t,e)},o.scheme=o.protocol,o.port=function(t,e){return this._parts.urn?void 0===t?"":this:(void 0!==t&&(0===t&&(t=null),t&&(":"===(t+="").charAt(0)&&(t=t.substring(1)),i.ensureValidPort(t))),x.call(this,t,e))},o.hostname=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0!==t){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(t,r))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');t=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(t,this._parts.protocol)}return C.call(this,t,e)},o.origin=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var s=i(t);return this.protocol(s.protocol()).authority(s.authority()).build(!e),this},o.host=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},o.authority=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},o.userinfo=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==t[t.length-1]&&(t+="@"),i.parseUserinfo(t,this._parts),this.build(!e),this},o.resource=function(t,e){var r;return void 0===t?this.path()+this.search()+this.hash():(r=i.parse(t),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!e),this)},o.subdomain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var s=this._parts.hostname.length-this.domain().length,n=this._parts.hostname.substring(0,s),o=new RegExp("^"+u(n));if(t&&"."!==t.charAt(t.length-1)&&(t+="."),-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");return t&&i.ensureValidHostname(t,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(o,t),this.build(!e),this},o.domain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var s=this._parts.hostname.length-this.tld(e).length-1;return s=this._parts.hostname.lastIndexOf(".",s-1)+1,this._parts.hostname.substring(s)||""}if(!t)throw new TypeError("cannot set domain empty");if(-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(t,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=t;else{var n=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(n,t)}return this.build(!e),this},o.tld=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var s=this._parts.hostname.lastIndexOf("."),n=this._parts.hostname.substring(s+1);return!0!==e&&r&&r.list[n.toLowerCase()]&&r.get(this._parts.hostname)||n}var i;if(!t)throw new TypeError("cannot set TLD empty");if(t.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(t))throw new TypeError('TLD "'+t+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}return this.build(!e),this},o.directory=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,s=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return t?i.decodePath(s):s}var n=this._parts.path.length-this.filename().length,o=this._parts.path.substring(0,n),a=new RegExp("^"+u(o));return this.is("relative")||(t||(t="/"),"/"!==t.charAt(0)&&(t="/"+t)),t&&"/"!==t.charAt(t.length-1)&&(t+="/"),t=i.recodePath(t),this._parts.path=this._parts.path.replace(a,t),this.build(!e),this},o.filename=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("string"!=typeof t){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),s=this._parts.path.substring(r+1);return t?i.decodePathSegment(s):s}var n=!1;"/"===t.charAt(0)&&(t=t.substring(1)),t.match(/\.?\//)&&(n=!0);var o=new RegExp(u(this.filename())+"$");return t=i.recodePath(t),this._parts.path=this._parts.path.replace(o,t),n?this.normalizePath(e):this.build(!e),this},o.suffix=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path||"/"===this._parts.path)return"";var r,s,n=this.filename(),o=n.lastIndexOf(".");return-1===o?"":(r=n.substring(o+1),s=/^[a-z0-9%]+$/i.test(r)?r:"",t?i.decodePathSegment(s):s)}"."===t.charAt(0)&&(t=t.substring(1));var a,l=this.suffix();if(l)a=t?new RegExp(u(l)+"$"):new RegExp(u("."+l)+"$");else{if(!t)return this;this._parts.path+="."+i.recodePath(t)}return a&&(t=i.recodePath(t),this._parts.path=this._parts.path.replace(a,t)),this.build(!e),this},o.segment=function(t,e,r){var s=this._parts.urn?":":"/",n=this.path(),i="/"===n.substring(0,1),o=n.split(s);if(void 0!==t&&"number"!=typeof t&&(r=e,e=t,t=void 0),void 0!==t&&"number"!=typeof t)throw new Error('Bad segment "'+t+'", must be 0-based integer');if(i&&o.shift(),t<0&&(t=Math.max(o.length+t,0)),void 0===e)return void 0===t?o:o[t];if(null===t||void 0===o[t])if(h(e)){o=[];for(var a=0,u=e.length;a<u;a++)(e[a].length||o.length&&o[o.length-1].length)&&(o.length&&!o[o.length-1].length&&o.pop(),o.push(m(e[a])))}else(e||"string"==typeof e)&&(e=m(e),""===o[o.length-1]?o[o.length-1]=e:o.push(e));else e?o[t]=m(e):o.splice(t,1);return i&&o.unshift(""),this.path(o.join(s),r)},o.segmentCoded=function(t,e,r){var s,n,o;if("number"!=typeof t&&(r=e,e=t,t=void 0),void 0===e){if(h(s=this.segment(t,e,r)))for(n=0,o=s.length;n<o;n++)s[n]=i.decode(s[n]);else s=void 0!==s?i.decode(s):void 0;return s}if(h(e))for(n=0,o=e.length;n<o;n++)e[n]=i.encode(e[n]);else e="string"==typeof e||e instanceof String?i.encode(e):e;return this.segment(t,e,r)};var O=o.query;return o.query=function(t,e){if(!0===t)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof t){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),s=t.call(this,r);return this._parts.query=i.buildQuery(s||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this}return void 0!==t&&"string"!=typeof t?(this._parts.query=i.buildQuery(t,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this):O.call(this,t,e)},o.setQuery=function(t,e,r){var s=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof t||t instanceof String)s[t]=void 0!==e?e:null;else{if("object"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var n in t)a.call(t,n)&&(s[n]=t[n])}return this._parts.query=i.buildQuery(s,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},o.addQuery=function(t,e,r){var s=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(s,t,void 0===e?null:e),this._parts.query=i.buildQuery(s,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},o.removeQuery=function(t,e,r){var s=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(s,t,e),this._parts.query=i.buildQuery(s,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},o.hasQuery=function(t,e,r){var s=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(s,t,e,r)},o.setSearch=o.setQuery,o.addSearch=o.addQuery,o.removeSearch=o.removeQuery,o.hasSearch=o.hasQuery,o.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},o.normalizeProtocol=function(t){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!t)),this},o.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&t?this._parts.hostname=t.toASCII(this._parts.hostname):this.is("IPv6")&&e&&(this._parts.hostname=e.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},o.normalizePort=function(t){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!t)),this},o.normalizePath=function(t){var e,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!t),this;if("/"===this._parts.path)return this;var s,n,o="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(e=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),e&&(o=r.substring(1).match(/^(\.\.\/)+/)||"")&&(o=o[0]);-1!==(s=r.search(/\/\.\.(\/|$)/));)0!==s?(-1===(n=r.substring(0,s).lastIndexOf("/"))&&(n=s),r=r.substring(0,n)+r.substring(s+3)):r=r.substring(3);return e&&this.is("relative")&&(r=o+r.substring(1)),this._parts.path=r,this.build(!t),this},o.normalizePathname=o.normalizePath,o.normalizeQuery=function(t){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!t)),this},o.normalizeFragment=function(t){return this._parts.fragment||(this._parts.fragment=null,this.build(!t)),this},o.normalizeSearch=o.normalizeQuery,o.normalizeHash=o.normalizeFragment,o.iso8859=function(){var t=i.encode,e=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},o.unicode=function(){var t=i.encode,e=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},o.readable=function(){var e=this.clone();e.username("").password("").normalize();var r="";if(e._parts.protocol&&(r+=e._parts.protocol+"://"),e._parts.hostname&&(e.is("punycode")&&t?(r+=t.toUnicode(e._parts.hostname),e._parts.port&&(r+=":"+e._parts.port)):r+=e.host()),e._parts.hostname&&e._parts.path&&"/"!==e._parts.path.charAt(0)&&(r+="/"),r+=e.path(!0),e._parts.query){for(var s="",n=0,o=e._parts.query.split("&"),a=o.length;n<a;n++){var u=(o[n]||"").split("=");s+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(s+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+s.substring(1)}return r+=i.decodeQuery(e.hash(),!0)},o.absoluteTo=function(t){var e,r,s,n=this.clone(),o=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t instanceof i||(t=new i(t)),n._parts.protocol)return n;if(n._parts.protocol=t._parts.protocol,this._parts.hostname)return n;for(r=0;s=o[r];r++)n._parts[s]=t._parts[s];return n._parts.path?(".."===n._parts.path.substring(-2)&&(n._parts.path+="/"),"/"!==n.path().charAt(0)&&(e=(e=t.directory())||(0===t.path().indexOf("/")?"/":""),n._parts.path=(e?e+"/":"")+n._parts.path,n.normalizePath())):(n._parts.path=t._parts.path,n._parts.query||(n._parts.query=t._parts.query)),n.build(),n},o.relativeTo=function(t){var e,r,s,n,o,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t=new i(t).normalize(),e=a._parts,r=t._parts,n=a.path(),o=t.path(),"/"!==n.charAt(0))throw new Error("URI is already relative");if("/"!==o.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(e.protocol===r.protocol&&(e.protocol=null),e.username!==r.username||e.password!==r.password)return a.build();if(null!==e.protocol||null!==e.username||null!==e.password)return a.build();if(e.hostname!==r.hostname||e.port!==r.port)return a.build();if(e.hostname=null,e.port=null,n===o)return e.path="",a.build();if(!(s=i.commonPath(n,o)))return a.build();var u=r.path.substring(s.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return e.path=u+e.path.substring(s.length)||"./",a.build()},o.equals=function(t){var e,r,s,n,o,u=this.clone(),l=new i(t),c={};if(u.normalize(),l.normalize(),u.toString()===l.toString())return!0;if(s=u.query(),n=l.query(),u.query(""),l.query(""),u.toString()!==l.toString())return!1;if(s.length!==n.length)return!1;for(o in e=i.parseQuery(s,this._parts.escapeQuerySpace),r=i.parseQuery(n,this._parts.escapeQuerySpace),e)if(a.call(e,o)){if(h(e[o])){if(!d(e[o],r[o]))return!1}else if(e[o]!==r[o])return!1;c[o]=!0}for(o in r)if(a.call(r,o)&&!c[o])return!1;return!0},o.preventInvalidHostname=function(t){return this._parts.preventInvalidHostname=!!t,this},o.duplicateQueryParameters=function(t){return this._parts.duplicateQueryParameters=!!t,this},o.escapeQuerySpace=function(t){return this._parts.escapeQuerySpace=!!t,this},i})},340:function(t,e,r){var s;t=r.nmd(t),function(n){e&&e.nodeType,t&&t.nodeType;var i="object"==typeof r.g&&r.g;i.global!==i&&i.window!==i&&i.self;var o,a=2147483647,u=36,l=1,h=26,c=38,p=700,d=72,m=128,f="-",g=/^xn--/,y=/[^\x20-\x7E]/,v=/[\x2E\u3002\uFF0E\uFF61]/g,b={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},S=u-l,_=Math.floor,w=String.fromCharCode;function E(t){throw new RangeError(b[t])}function x(t,e){for(var r=t.length,s=[];r--;)s[r]=e(t[r]);return s}function C(t,e){var r=t.split("@"),s="";return r.length>1&&(s=r[0]+"@",t=r[1]),s+x((t=t.replace(v,".")).split("."),e).join(".")}function O(t){for(var e,r,s=[],n=0,i=t.length;n<i;)(e=t.charCodeAt(n++))>=55296&&e<=56319&&n<i?56320==(64512&(r=t.charCodeAt(n++)))?s.push(((1023&e)<<10)+(1023&r)+65536):(s.push(e),n--):s.push(e);return s}function A(t){return x(t,function(t){var e="";return t>65535&&(e+=w((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+=w(t)}).join("")}function T(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function P(t,e,r){var s=0;for(t=r?_(t/p):t>>1,t+=_(t/e);t>S*h>>1;s+=u)t=_(t/S);return _(s+(S+1)*t/(t+c))}function I(t){var e,r,s,n,i,o,c,p,g,y,v,b=[],S=t.length,w=0,x=m,C=d;for((r=t.lastIndexOf(f))<0&&(r=0),s=0;s<r;++s)t.charCodeAt(s)>=128&&E("not-basic"),b.push(t.charCodeAt(s));for(n=r>0?r+1:0;n<S;){for(i=w,o=1,c=u;n>=S&&E("invalid-input"),((p=(v=t.charCodeAt(n++))-48<10?v-22:v-65<26?v-65:v-97<26?v-97:u)>=u||p>_((a-w)/o))&&E("overflow"),w+=p*o,!(p<(g=c<=C?l:c>=C+h?h:c-C));c+=u)o>_(a/(y=u-g))&&E("overflow"),o*=y;C=P(w-i,e=b.length+1,0==i),_(w/e)>a-x&&E("overflow"),x+=_(w/e),w%=e,b.splice(w++,0,x)}return A(b)}function N(t){var e,r,s,n,i,o,c,p,g,y,v,b,S,x,C,A=[];for(b=(t=O(t)).length,e=m,r=0,i=d,o=0;o<b;++o)(v=t[o])<128&&A.push(w(v));for(s=n=A.length,n&&A.push(f);s<b;){for(c=a,o=0;o<b;++o)(v=t[o])>=e&&v<c&&(c=v);for(c-e>_((a-r)/(S=s+1))&&E("overflow"),r+=(c-e)*S,e=c,o=0;o<b;++o)if((v=t[o])<e&&++r>a&&E("overflow"),v==e){for(p=r,g=u;!(p<(y=g<=i?l:g>=i+h?h:g-i));g+=u)C=p-y,x=u-y,A.push(w(T(y+C%x,0))),p=_(C/x);A.push(w(T(p,0))),i=P(r,S,s==n),r=0,++s}++r,++e}return A.join("")}o={version:"1.3.2",ucs2:{decode:O,encode:A},decode:I,encode:N,toASCII:function(t){return C(t,function(t){return y.test(t)?"xn--"+N(t):t})},toUnicode:function(t){return C(t,function(t){return g.test(t)?I(t.slice(4).toLowerCase()):t})}},void 0===(s=function(){return o}.call(e,r,e,t))||(t.exports=s)}()}},e={};function r(s){var n=e[s];if(void 0!==n)return n.exports;var i=e[s]={id:s,loaded:!1,exports:{}};return t[s].call(i.exports,i,i.exports,r),i.loaded=!0,i.exports}r.n=(t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e}),r.d=((t,e)=>{for(var s in e)r.o(e,s)&&!r.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}),r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=((t,e)=>Object.prototype.hasOwnProperty.call(t,e)),r.nmd=(t=>(t.paths=[],t.children||(t.children=[]),t)),(()=>{"use strict";class t{constructor(t,e,r){this.x=t?parseFloat(t):0,this.y=e?parseFloat(e):0,this.mode=r,this.CLASS_NAME="SuperMap.Pixel"}toString(){return"x="+this.x+",y="+this.y}clone(){return new t(this.x,this.y,this.mode)}equals(t){var e=!1;return null!=t&&(e=this.x==t.x&&this.y==t.y||isNaN(this.x)&&isNaN(this.y)&&isNaN(t.x)&&isNaN(t.y)),e}distanceTo(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}add(e,r){if(null==e||null==r)throw new TypeError("Pixel.add cannot receive null values");return new t(this.x+e,this.y+r)}offset(t){var e=this.clone();return t&&(e=this.add(t.x,t.y)),e}destroy(){this.x=null,this.y=null,this.mode=null}}t.Mode={LeftTop:"lefttop",RightTop:"righttop",RightBottom:"rightbottom",LeftBottom:"leftbottom"};var e={startsWith:function(t,e){return 0==t.indexOf(e)},contains:function(t,e){return-1!=t.indexOf(e)},trim:function(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")},camelize:function(t){for(var e=t.split("-"),r=e[0],s=1,n=e.length;s<n;s++){var i=e[s];r+=i.charAt(0).toUpperCase()+i.substring(1)}return r},format:function(t,r,s){r||(r=window);return t.replace(e.tokenRegEx,function(t,e){for(var n,i=e.split(/\.+/),o=0;o<i.length;o++)0==o&&(n=r),n=n[i[o]];return"function"==typeof n&&(n=s?n.apply(null,s):n()),void 0===n?"undefined":n})},tokenRegEx:/\$\{([\w.]+?)\}/g,numberRegEx:/^([+-]?)(?=\d|\.\d)\d*(\.\d*)?([Ee]([+-]?\d+))?$/,isNumeric:function(t){return e.numberRegEx.test(t)},numericIf:function(t){return e.isNumeric(t)?parseFloat(t):t}},s={decimalSeparator:".",thousandsSeparator:",",limitSigDigs:function(t,e){var r=0;return e>0&&(r=parseFloat(t.toPrecision(e))),r},format:function(t,e,r,n){e=void 0!==e?e:0,r=void 0!==r?r:s.thousandsSeparator,n=void 0!==n?n:s.decimalSeparator,null!=e&&(t=parseFloat(t.toFixed(e)));var i=t.toString().split(".");1===i.length&&null==e&&(e=0);var o,a=i[0];if(r)for(var u=/(-?[0-9]+)([0-9]{3})/;u.test(a);)a=a.replace(u,"$1"+r+"$2");if(0==e)o=a;else{var l=i.length>1?i[1]:"0";null!=e&&(l+=new Array(e-l.length+1).join("0")),o=a+n+l}return o}};Number.prototype.limitSigDigs||(Number.prototype.limitSigDigs=function(t){return s.limitSigDigs(this,t)});var n={bind:function(t,e){var r=Array.prototype.slice.apply(arguments,[2]);return function(){var s=r.concat(Array.prototype.slice.apply(arguments,[0]));return t.apply(e,s)}},bindAsEventListener:function(t,e){return function(r){return t.call(e,r||window.event)}},False:function(){return!1},True:function(){return!0},Void:function(){}};class i{constructor(){this.CLASS_NAME="SuperMap.Geometry",this.id=h.createUniqueID(this.CLASS_NAME+"_"),this.parent=null,this.bounds=null,this.SRID=null}destroy(){this.id=null,this.bounds=null,this.SRID=null}clone(){return new i}setBounds(t){t&&(this.bounds=t.clone())}clearBounds(){this.bounds=null,this.parent&&this.parent.clearBounds()}extendBounds(t){this.getBounds()?this.bounds.extend(t):this.setBounds(t)}getBounds(){return null==this.bounds&&this.calculateBounds(),this.bounds}calculateBounds(){}getVertices(t){}getArea(){return 0}}var o=r(193),a=r.n(o);const u=function(){var t,e="",r="",s="pc",n=navigator.userAgent.toLowerCase();return n.indexOf("msie")>-1||n.indexOf("trident")>-1&&n.indexOf("rv")>-1?(e="msie",t=n.match(/msie ([\d.]+)/)||n.match(/rv:([\d.]+)/)):n.indexOf("chrome")>-1?(e="chrome",t=n.match(/chrome\/([\d.]+)/)):n.indexOf("firefox")>-1?(e="firefox",t=n.match(/firefox\/([\d.]+)/)):n.indexOf("opera")>-1?(e="opera",t=n.match(/version\/([\d.]+)/)):n.indexOf("safari")>-1&&(e="safari",t=n.match(/version\/([\d.]+)/)),r=t?t[1]:"",n.indexOf("ipad")>-1||n.indexOf("ipod")>-1||n.indexOf("iphone")>-1?s="apple":n.indexOf("android")>-1&&(r=(t=n.match(/version\/([\d.]+)/))?t[1]:"",s="android"),{name:e,version:r,device:s}}(),l=function(){var t=!0,e=u;return document.createElement("canvas").getContext?("firefox"===e.name&&parseFloat(e.version)<5&&(t=!1),"safari"===e.name&&parseFloat(e.version)<4&&(t=!1),"opera"===e.name&&parseFloat(e.version)<10&&(t=!1),"msie"===e.name&&parseFloat(e.version)<9&&(t=!1)):t=!1,t}(),h=(function(){var t=navigator.userAgent.toLowerCase();-1===t.indexOf("webkit")&&t.indexOf("gecko")}(),{assign(t){for(var e=0;e<Object.getOwnPropertyNames(arguments).length;e++){var r=Object.getOwnPropertyNames(arguments)[e];if("caller"!=r&&"callee"!=r&&"length"!=r&&"arguments"!=r){var s=arguments[r];if(s)for(var n=0;n<Object.getOwnPropertyNames(s).length;n++){var i=Object.getOwnPropertyNames(s)[n];"caller"!=r&&"callee"!=r&&"length"!=r&&"arguments"!=r&&(t[i]=s[i])}}}return t},extend:function(t,e){if(t=t||{},e){for(var r in e){var s=e[r];void 0!==s&&(t[r]=s)}!("function"==typeof window.Event&&e instanceof window.Event)&&e.hasOwnProperty&&e.hasOwnProperty("toString")&&(t.toString=e.toString)}return t},copy:function(t,e){var r;if(t=t||{},e)for(var s in t)void 0!==(r=e[s])&&(t[s]=r)},reset:function(t){for(var e in t=t||{})if(t.hasOwnProperty(e)){if("object"==typeof t[e]&&t[e]instanceof Array){for(var r in t[e])t[e][r].destroy&&t[e][r].destroy();t[e].length=0}else"object"==typeof t[e]&&t[e]instanceof Object&&t[e].destroy&&t[e].destroy();t[e]=null}},getElement:function(){for(var t=[],e=0,r=arguments.length;e<r;e++){var s=arguments[e];if("string"==typeof s&&(s=document.getElementById(s)),1===arguments.length)return s;t.push(s)}return t},isElement:function(t){return!(!t||1!==t.nodeType)},isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},removeItem:function(t,e){for(var r=t.length-1;r>=0;r--)t[r]===e&&t.splice(r,1);return t},indexOf:function(t,e){if(null==t)return-1;if("function"==typeof t.indexOf)return t.indexOf(e);for(var r=0,s=t.length;r<s;r++)if(t[r]===e)return r;return-1},modifyDOMElement:function(t,e,r,s,n,i,o,a){e&&(t.id=e),r&&(t.style.left=r.x+"px",t.style.top=r.y+"px"),s&&(t.style.width=s.w+"px",t.style.height=s.h+"px"),n&&(t.style.position=n),i&&(t.style.border=i),o&&(t.style.overflow=o),parseFloat(a)>=0&&parseFloat(a)<1?(t.style.filter="alpha(opacity="+100*a+")",t.style.opacity=a):1===parseFloat(a)&&(t.style.filter="",t.style.opacity="")},applyDefaults:function(t,e){t=t||{};var r="function"==typeof window.Event&&e instanceof window.Event;for(var s in e)(void 0===t[s]||!r&&e.hasOwnProperty&&e.hasOwnProperty(s)&&!t.hasOwnProperty(s))&&(t[s]=e[s]);return!r&&e&&e.hasOwnProperty&&e.hasOwnProperty("toString")&&!t.hasOwnProperty("toString")&&(t.toString=e.toString),t},getParameterString:function(t){var e=[];for(var r in t){var s,n=t[r];if(null!=n&&"function"!=typeof n)s=Array.isArray(n)||"[object Object]"===n.toString()?encodeURIComponent(JSON.stringify(n)):encodeURIComponent(n),e.push(encodeURIComponent(r)+"="+s)}return e.join("&")},urlAppend:function(t,e){var r=t;if(e){0===e.indexOf("?")&&(e=e.substring(1));var s=(t+" ").split(/[?&]/);r+=" "===s.pop()?e:s.length?"&"+e:"?"+e}return r},urlPathAppend:function(t,e){let r=t;if(!e)return r;0===e.indexOf("/")&&(e=e.substring(1));const s=t.split("?");return s[0].indexOf("/",s[0].length-1)<0&&(s[0]+="/"),r=`${s[0]}${e}${s.length>1?`?${s[1]}`:""}`},DEFAULT_PRECISION:14,toFloat:function(t,e){return null==e&&(e=h.DEFAULT_PRECISION),"number"!=typeof t&&(t=parseFloat(t)),0===e?t:parseFloat(t.toPrecision(e))},rad:function(t){return t*Math.PI/180},getParameters:function(t){t=null===t||void 0===t?window.location.href:t;var r="";if(e.contains(t,"?")){var s=t.indexOf("?")+1,n=e.contains(t,"#")?t.indexOf("#"):t.length;r=t.substring(s,n)}for(var i={},o=r.split(/[&;]/),a=0,u=o.length;a<u;++a){var l=o[a].split("=");if(l[0]){var h=l[0];try{h=decodeURIComponent(h)}catch(t){h=unescape(h)}var c=(l[1]||"").replace(/\+/g," ");try{c=decodeURIComponent(c)}catch(t){c=unescape(c)}1==(c=c.split(",")).length&&(c=c[0]),i[h]=c}}return i},lastSeqID:0,createUniqueID:function(t){return null==t&&(t="id_"),h.lastSeqID+=1,t+h.lastSeqID},normalizeScale:function(t){return t>1?1/t:t},getResolutionFromScale:function(t,e){var r;t&&(null==e&&(e="degrees"),r=1/(h.normalizeScale(t)*c[e]*96));return r},getScaleFromResolution:function(t,e){return null==e&&(e="degrees"),t*c[e]*96},getBrowser:function(){return u},isSupportCanvas:l,supportCanvas:function(){return h.isSupportCanvas},isInTheSameDomain:function(t){if(!t)return!0;return-1===t.indexOf("//")||h.isSameDomain(t,document.location.toString())},isSameDomain:(t,e)=>new(a())(t).normalize().origin()===new(a())(e).normalize().origin(),calculateDpi:function(t,e,r,s,n){if(t&&e&&r){var i,o=t.getWidth(),a=t.getHeight(),u=e.w,l=e.h;if(n=n||6378137,"degree"===(s=s||"degrees").toLowerCase()||"degrees"===s.toLowerCase()||"dd"===s.toLowerCase()){let t=o/u,e=a/l;i=254/(t>e?t:e)/r/(2*Math.PI*n/360)/1e4}else{i=254/(o/u)/r/1e4}return i}},toJSON:function(t){var e=t;if(null==e)return null;switch(e.constructor){case String:return e=(e=(e=(e=(e=(e=(e='"'+e.replace(/(["\\])/g,"\\$1")+'"').replace(/\n/g,"\\n")).replace(/\r/g,"\\r")).replace("<","&lt;")).replace(">","&gt;")).replace(/%/g,"%25")).replace(/&/g,"%26");case Array:for(var r="",s=0,n=e.length;s<n;s++)r+=h.toJSON(e[s]),s!==e.length-1&&(r+=",");return"["+r+"]";case Number:return isFinite(e)?String(e):null;case Boolean:return String(e);case Date:return"{'__type':\"System.DateTime\",'Year':"+e.getFullYear()+",'Month':"+(e.getMonth()+1)+",'Day':"+e.getDate()+",'Hour':"+e.getHours()+",'Minute':"+e.getMinutes()+",'Second':"+e.getSeconds()+",'Millisecond':"+e.getMilliseconds()+",'TimezoneOffset':"+e.getTimezoneOffset()+"}";default:if(null!=e.toJSON&&"function"==typeof e.toJSON)return e.toJSON();if("object"==typeof e){if(e.length){let t=[];for(let r=0,s=e.length;r<s;r++)t.push(h.toJSON(e[r]));return"["+t.join(",")+"]"}let t=[];for(let r in e)"function"!=typeof e[r]&&"CLASS_NAME"!==r&&"parent"!==r&&t.push("'"+r+"':"+h.toJSON(e[r]));return t.length>0?"{"+t.join(",")+"}":"{}"}return e.toString()}},getResolutionFromScaleDpi:function(t,e,r,s){return s=s||6378137,r=r||"",t>0&&e>0?(t=h.normalizeScale(t),"degree"===r.toLowerCase()||"degrees"===r.toLowerCase()||"dd"===r.toLowerCase()?254/e/t/(2*Math.PI*s/360)/1e4:254/e/t/1e4):-1},getScaleFromResolutionDpi:function(t,e,r,s){return s=s||6378137,r=r||"",t>0&&e>0?"degree"===r.toLowerCase()||"degrees"===r.toLowerCase()||"dd"===r.toLowerCase()?254/e/t/(2*Math.PI*s/360)/1e4:254/e/t/1e4:-1},transformResult:function(t){return t.responseText&&"string"==typeof t.responseText&&(t=JSON.parse(t.responseText)),t},copyAttributes:function(t,e){if(t=t||{},e)for(var r in e){var s=e[r];void 0!==s&&"CLASS_NAME"!==r&&"function"!=typeof s&&(t[r]=s)}return t},copyAttributesWithClip:function(t,e,r){if(t=t||{},e)for(var s in e){var n=!1;if(r&&r.length)for(var i=0,o=r.length;i<o;i++)if(s===r[i]){n=!0;break}if(!0!==n){var a=e[s];void 0!==a&&"CLASS_NAME"!==s&&"function"!=typeof a&&(t[s]=a)}}return t},cloneObject:function(t){if(null===t||"object"!=typeof t)return t;if(t instanceof Date){let e=new Date;return e.setTime(t.getTime()),e}if(t instanceof Array){return t.slice(0)}if(t instanceof Object){let r={};for(var e in t)t.hasOwnProperty(e)&&(r[e]=h.cloneObject(t[e]));return r}throw new Error("Unable to copy obj! Its type isn't supported.")},lineIntersection:function(t,e,r,s){var n,o,a=null,u=(s.x-r.x)*(t.y-r.y)-(s.y-r.y)*(t.x-r.x),l=(e.x-t.x)*(t.y-r.y)-(e.y-t.y)*(t.x-r.x),h=(s.y-r.y)*(e.x-t.x)-(s.x-r.x)*(e.y-t.y);if(0!=h)o=l/h,a=(n=u/h)>=0&&o<=1&&n<=1&&o>=0?new i.Point(t.x+n*(e.x-t.x),t.y+n*(e.y-t.y)):"No Intersection";else if(0==u&&0==l){var c=Math.max(t.y,e.y),p=Math.min(t.y,e.y),d=Math.max(t.x,e.x),m=Math.min(t.x,e.x);a=(r.y>=p&&r.y<=c||s.y>=p&&s.y<=c)&&r.x>=m&&r.x<=d||s.x>=m&&s.x<=d?"Coincident":"Parallel"}else a="Parallel";return a},getTextBounds:function(t,e,r){document.body.appendChild(r),r.style.width="auto",r.style.height="auto",t.fontSize&&(r.style.fontSize=t.fontSize),t.fontFamily&&(r.style.fontFamily=t.fontFamily),t.fontWeight&&(r.style.fontWeight=t.fontWeight),r.style.position="relative",r.style.visibility="hidden",r.style.display="inline-block",r.innerHTML=e;var s=r.clientWidth,n=r.clientHeight;return document.body.removeChild(r),{textWidth:s,textHeight:n}},convertPath:function(t,e){return e?t.replace(/\{([\w-\.]+)\}/g,(t,r)=>{var s;return s=e.hasOwnProperty(r)?function(t){if(void 0==t||null==t)return"";if(t instanceof Date)return t.toJSON();if(function(t){if("string"!=typeof t&&"object"!=typeof t)return!1;try{const e=t.toString();return"[object Object]"===e||"[object Array]"===e}catch(t){return!1}}(t))return JSON.stringify(t);return t.toString()}(e[r]):t,encodeURIComponent(s)}):t},hexToRgba(t,e){var r=[],s=[];if(3==(t=t.replace(/#/,"")).length){var n=[];for(let e=0;e<3;e++)n.push(t.charAt(e)+t.charAt(e));t=n.join("")}for(let e=0;e<6;e+=2)r[e]="0x"+t.substr(e,2),s.push(parseInt(Number(r[e])));return s.push(e),"rgba("+s.join(",")+")"}}),c={inches:1,ft:12,mi:63360,m:39.3701,km:39370.1,dd:4374754,yd:36};c.in=c.inches,c.degrees=c.dd,c.nmi=1852*c.m;h.extend(c,{Inch:c.inches,Meter:39.37,Foot:12,IFoot:11.999976,ClarkeFoot:11.999868327581488,SearsFoot:11.999955194477684,GoldCoastFoot:11.999964589846002,IInch:.9999979999999999,MicroInch:999998e-9,Mil:9.99998e-7,Centimeter:.3937,Kilometer:39370,Yard:36,SearsYard:35.99986558343306,IndianYard:35.99987015540864,IndianYd37:35.999740205100004,IndianYd62:35.999880755999996,IndianYd75:35.999868945,IndianFoot:11.9999567087,IndianFt37:11.9999134017,IndianFt62:11.999960252000001,IndianFt75:11.999956315,Mile:63360,IYard:35.999928,IMile:63359.87328,NautM:72913.24,"Lat-66":4367838.370169282,"Lat-83":4367954.152606599,Decimeter:3.9370000000000003,Millimeter:.03937,Dekameter:393.7,Decameter:393.7,Hectometer:3937,GermanMeter:39.370535294205006,CaGrid:39.359685060000004,ClarkeChain:791.************,GunterChain:792.0000000000001,BenoitChain:791.9977268035781,SearsChain:791.9970428354235,ClarkeLink:7.91************,GunterLink:7.920000000000001,BenoitLink:7.919977268035781,SearsLink:7.919970428354236,Rod:198.00000000000014,IntnlChain:791.998416,IntnlLink:7.91998416,Perch:198.00000000000014,Pole:198.00000000000014,Furlong:7919.************,Rood:148.75036777426,CapeFoot:11.999868185255002,Brealey:14763.75,ModAmFt:12.000458400000001,Fathom:71.999856,"NautM-UK":72959.85408,"50kilometers":1968500,"150kilometers":5905500}),h.extend(c,{mm:c.Meter/1e3,cm:c.Meter/100,dm:100*c.Meter,km:1e3*c.Meter,kmi:c.nmi,fath:c.Fathom,ch:c.IntnlChain,link:c.IntnlLink,"us-in":c.inches,"us-ft":c.Foot,"us-yd":c.Yard,"us-ch":c.GunterChain,"us-mi":c.Mile,"ind-yd":c.IndianYd37,"ind-ft":c.IndianFt37,"ind-ch":791.9942845122}),c.degree=c.dd,c.meter=c.m,c.foot=c.ft,c.inch=c.inches,c.mile=c.mi,c.kilometer=c.km,c.yard=c.yd;var p={observers:!1,KEY_SPACE:32,KEY_BACKSPACE:8,KEY_TAB:9,KEY_RETURN:13,KEY_ESC:27,KEY_LEFT:37,KEY_UP:38,KEY_RIGHT:39,KEY_DOWN:40,KEY_DELETE:46,element:function(t){return t.target||t.srcElement},isSingleTouch:function(t){return t.touches&&1===t.touches.length},isMultiTouch:function(t){return t.touches&&t.touches.length>1},isLeftClick:function(t){return t.which&&1===t.which||t.button&&1===t.button},isRightClick:function(t){return t.which&&3===t.which||t.button&&2===t.button},stop:function(t,e){e||(t.preventDefault?t.preventDefault():t.returnValue=!1),t.stopPropagation?t.stopPropagation():t.cancelBubble=!0},findElement:function(t,e){for(var r=p.element(t);r.parentNode&&(!r.tagName||r.tagName.toUpperCase()!=e.toUpperCase());)r=r.parentNode;return r},observe:function(t,e,r,s){var n=h.getElement(t);if(s=s||!1,"keypress"===e&&(navigator.appVersion.match(/Konqueror|Safari|KHTML/)||n.attachEvent)&&(e="keydown"),this.observers||(this.observers={}),!n._eventCacheID){var i="eventCacheID_";n.id&&(i=n.id+"_"+i),n._eventCacheID=h.createUniqueID(i)}var o=n._eventCacheID;this.observers[o]||(this.observers[o]=[]),this.observers[o].push({element:n,name:e,observer:r,useCapture:s}),n.addEventListener?"mousewheel"===e?n.addEventListener(e,r,{useCapture:s,passive:!1}):n.addEventListener(e,r,s):n.attachEvent&&n.attachEvent("on"+e,r)},stopObservingElement:function(t){var e=h.getElement(t)._eventCacheID;this._removeElementObservers(p.observers[e])},_removeElementObservers:function(t){if(t)for(var e=t.length-1;e>=0;e--){var r=t[e],s=new Array(r.element,r.name,r.observer,r.useCapture);p.stopObserving.apply(this,s)}},stopObserving:function(t,e,r,s){s=s||!1;var n=h.getElement(t),i=n._eventCacheID;"keypress"===e&&(navigator.appVersion.match(/Konqueror|Safari|KHTML/)||n.detachEvent)&&(e="keydown");var o=!1,a=p.observers[i];if(a)for(var u=0;!o&&u<a.length;){var l=a[u];if(l.name===e&&l.observer===r&&l.useCapture===s){a.splice(u,1),0==a.length&&delete p.observers[i],o=!0;break}u++}return o&&(n.removeEventListener?n.removeEventListener(e,r,s):n&&n.detachEvent&&n.detachEvent("on"+e,r)),o},unloadCache:function(){if(p&&p.observers){for(var t in p.observers){var e=p.observers[t];p._removeElementObservers.apply(this,[e])}p.observers=!1}},CLASS_NAME:"SuperMap.Event"};p.observe(window,"resize",p.unloadCache,!1);class d{constructor(t,e,r,s,n){if(this.BROWSER_EVENTS=["mouseover","mouseout","mousedown","mouseup","mousemove","click","dblclick","rightclick","dblrightclick","resize","focus","blur","touchstart","touchmove","touchend","keydown","MSPointerDown","MSPointerUp","pointerdown","pointerup","MSGestureStart","MSGestureChange","MSGestureEnd","contextmenu"],this.listeners={},this.object=t,this.element=null,this.eventTypes=[],this.eventHandler=null,this.fallThrough=s,this.includeXY=!1,this.extensions={},this.extensionCount={},this.clearMouseListener=null,h.extend(this,n),null!=r)for(var i=0,o=r.length;i<o;i++)this.addEventType(r[i]);null!=e&&this.attachToElement(e),this.CLASS_NAME="SuperMap.Events"}destroy(){for(var t in this.extensions)"boolean"!=typeof this.extensions[t]&&this.extensions[t].destroy();this.extensions=null,this.element&&(p.stopObservingElement(this.element),this.element.hasScrollEvent&&p.stopObserving(window,"scroll",this.clearMouseListener)),this.element=null,this.listeners=null,this.object=null,this.eventTypes=null,this.fallThrough=null,this.eventHandler=null}addEventType(t){this.listeners[t]||(this.eventTypes.push(t),this.listeners[t]=[])}attachToElement(t){this.element?p.stopObservingElement(this.element):(this.eventHandler=n.bindAsEventListener(this.handleBrowserEvent,this),this.clearMouseListener=n.bind(this.clearMouseCache,this)),this.element=t;for(var e=0,r=this.BROWSER_EVENTS.length;e<r;e++){var s=this.BROWSER_EVENTS[e];this.addEventType(s),p.observe(t,s,this.eventHandler)}p.observe(t,"dragstart",p.stop)}on(t){for(var e in t)"scope"!==e&&t.hasOwnProperty(e)&&this.register(e,t.scope,t[e])}register(t,e,r,s){if(t in d&&!this.extensions[t]&&(this.extensions[t]=new d[t](this)),null!=r&&-1!==h.indexOf(this.eventTypes,t)){null==e&&(e=this.object);var n=this.listeners[t];n||(n=[],this.listeners[t]=n,this.extensionCount[t]=0);var i={obj:e,func:r};s?(n.splice(this.extensionCount[t],0,i),"object"==typeof s&&s.extension&&this.extensionCount[t]++):n.push(i)}}registerPriority(t,e,r){this.register(t,e,r,!0)}un(t){for(var e in t)"scope"!==e&&t.hasOwnProperty(e)&&this.unregister(e,t.scope,t[e])}unregister(t,e,r){null==e&&(e=this.object);var s=this.listeners[t];if(null!=s)for(var n=0,i=s.length;n<i;n++)if(s[n].obj===e&&s[n].func===r){s.splice(n,1);break}}remove(t){null!=this.listeners[t]&&(this.listeners[t]=[])}triggerEvent(t,e){var r=this.listeners[t];if(r&&0!=r.length){var s;null==e&&(e={}),e.object=this.object,e.element=this.element,e.type||(e.type=t);for(var n=0,i=(r=r.slice()).length;n<i;n++){var o=r[n];if(void 0!=(s=o.func.apply(o.obj,[e]))&&!1===s)break}return this.fallThrough||p.stop(e,!0),s}}handleBrowserEvent(t){var e=t.type,r=this.listeners[e];if(r&&0!=r.length){var s=t.touches;if(s&&s[0]){for(var n,i=0,o=0,a=s.length,u=0;u<a;++u)i+=(n=s[u]).clientX,o+=n.clientY;t.clientX=i/a,t.clientY=o/a}this.includeXY&&(t.xy=this.getMousePosition(t)),this.triggerEvent(e,t)}}clearMouseCache(){this.element.scrolls=null,this.element.lefttop=null;var t=document.body;t&&(0==t.scrollTop&&0==t.scrollLeft||!navigator.userAgent.match(/iPhone/i))&&(this.element.offsets=null)}getMousePosition(e){if(this.includeXY?this.element.hasScrollEvent||(p.observe(window,"scroll",this.clearMouseListener),this.element.hasScrollEvent=!0):this.clearMouseCache(),!this.element.scrolls){var r=h.getViewportElement();this.element.scrolls=[r.scrollLeft,r.scrollTop]}return this.element.lefttop||(this.element.lefttop=[document.documentElement.clientLeft||0,document.documentElement.clientTop||0]),this.element.offsets||(this.element.offsets=h.pagePosition(this.element)),new t(e.clientX+this.element.scrolls[0]-this.element.offsets[0]-this.element.lefttop[0],e.clientY+this.element.scrolls[1]-this.element.offsets[1]-this.element.lefttop[1])}}d.prototype.BROWSER_EVENTS=["mouseover","mouseout","mousedown","mouseup","mousemove","click","dblclick","rightclick","dblrightclick","resize","focus","blur","touchstart","touchmove","touchend","keydown","MSPointerDown","MSPointerUp","pointerdown","pointerup","MSGestureStart","MSGestureChange","MSGestureEnd","contextmenu"];r(836),r(444);var m=r(348),f=r.n(m);let g=window.fetch;var y,v,b={limitLength:1500,queryKeys:[],queryValues:[],supermap_callbacks:{},addQueryStrings:function(t){for(var e in t){this.queryKeys.push(e),"string"!=typeof t[e]&&(t[e]=h.toJSON(t[e]));var r=encodeURIComponent(t[e]);this.queryValues.push(r)}},issue:function(t){for(var e=this,r=e.getUid(),s=t.url,n=[],i=s,o=0,a=e.queryKeys?e.queryKeys.length:0,u=0;u<a;u++)if(i.length+e.queryKeys[u].length+2>=e.limitLength){if(0==o)return!1;n.push(i),i=s,o=0,u--}else if(i.length+e.queryKeys[u].length+2+e.queryValues[u].length>e.limitLength)for(var l=e.queryValues[u];l.length>0;){var h=e.limitLength-i.length-e.queryKeys[u].length-2;i.indexOf("?")>-1?i+="&":i+="?";var c=l.substring(0,h);"%"===c.substring(h-1,h)?(h-=1,c=l.substring(0,h)):"%"===c.substring(h-2,h-1)&&(h-=2,c=l.substring(0,h)),i+=e.queryKeys[u]+"="+c,l=l.substring(h),c.length>0&&(n.push(i),i=s,o=0)}else o++,i.indexOf("?")>-1?i+="&":i+="?",i+=e.queryKeys[u]+"="+e.queryValues[u];return n.push(i),e.send(n,"SuperMapJSONPCallbacks_"+r,t&&t.proxy)},getUid:function(){return 1e3*(new Date).getTime()+Math.floor(1e17*Math.random())},send:function(t,e,r){var s=t.length;if(s>0)return new Promise(n=>{for(var i=(new Date).getTime(),o=0;o<s;o++){var a=t[o];a.indexOf("?")>-1?a+="&":a+="?",a+="sectionCount="+s,a+="&sectionIndex="+o,a+="&jsonpUserID="+i,r&&(a=decodeURIComponent(a),a=r+encodeURIComponent(a)),f()(a,{jsonpCallbackFunction:e,timeout:3e4}).then(t=>{n(t.json())})}})},GET:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings(t.params),this.issue(t)},POST:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)},PUT:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)},DELETE:function(t){return this.queryKeys.length=0,this.queryValues.length=0,this.addQueryStrings({requestEntity:t.data}),this.issue(t)}},S=function(){return v||45e3},_={commit:function(t,e,r,s){switch(t=t?t.toUpperCase():t){case"GET":return this.get(e,r,s);case"POST":return this.post(e,r,s);case"PUT":return this.put(e,r,s);case"DELETE":return this.delete(e,r,s);default:return this.get(e,r,s)}},supportDirectRequest:function(t,e){return!!h.isInTheSameDomain(t)||(void 0!=e.crossOrigin?e.crossOrigin:(void 0!=y?y:window.XMLHttpRequest&&"withCredentials"in new window.XMLHttpRequest)||e.proxy)},get:function(t,e,r){r=r||{};if(t=h.urlAppend(t,this._getParameterString(e||{})),t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){var s={url:t=t.replace(".json",".jsonp"),data:e};return b.GET(s)}return this.urlIsLong(t)?this._postSimulatie("GET",t.substring(0,t.indexOf("?")),e,r):this._fetch(t,e,r,"GET")},delete:function(t,e,r){r=r||{};if(t=h.urlAppend(t,this._getParameterString(e||{})),t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var s={url:t+="&_method=DELETE",data:e};return b.DELETE(s)}return this.urlIsLong(t)?this._postSimulatie("DELETE",t.substring(0,t.indexOf("?")),e,r):this._fetch(t,e,r,"DELETE")},post:function(t,e,r){if(r=r||{},t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var s={url:h.urlAppend(t,"_method=POST"),data:e};return b.POST(s)}return this._fetch(t,e,r,"POST")},put:function(t,e,r){if(r=r||{},t=this._processUrl(t,r),!this.supportDirectRequest(t,r)){t=t.replace(".json",".jsonp");var s={url:t+="&_method=PUT",data:e};return b.PUT(s)}return this._fetch(t,e,r,"PUT")},urlIsLong:function(t){for(var e=0,r=null,s=0,n=t.length;s<n;s++)(r=t.charCodeAt(s))<127?e++:128<=r&&r<=2047?e+=2:2048<=r&&r<=65535&&(e+=3);return!(e<2e3)},_postSimulatie:function(t,e,r,s){return e+=(e.indexOf("?")>-1?"&":"?")+"_method="+t,"string"!=typeof r&&(r=JSON.stringify(r)),this.post(e,r,s)},_processUrl:function(t,e){if(this._isMVTRequest(t))return t;if(-1===t.indexOf(".json")&&!e.withoutFormatSuffix)if(t.indexOf("?")<0)t+=".json";else{var r=t.split("?");2===r.length&&(t=r[0]+".json?"+r[1])}return e&&e.proxy&&("function"==typeof e.proxy?t=e.proxy(t):(t=decodeURIComponent(t),t=e.proxy+encodeURIComponent(t))),t},_fetch:function(t,e,r,s){return(r=r||{}).headers=r.headers||{},r.headers["Content-Type"]||FormData.prototype.isPrototypeOf(e)||(r.headers["Content-Type"]="application/x-www-form-urlencoded;charset=UTF-8"),r.timeout?this._timeout(r.timeout,g(t,{method:s,headers:r.headers,body:"PUT"===s||"POST"===s?e:void 0,credentials:this._getWithCredentials(r),mode:"cors",timeout:S()}).then(function(t){return t})):g(t,{method:s,body:"PUT"===s||"POST"===s?e:void 0,headers:r.headers,credentials:this._getWithCredentials(r),mode:"cors",timeout:S()}).then(function(t){return t})},_getWithCredentials:function(t){return!0===t.withCredentials?"include":!1===t.withCredentials?"omit":"same-origin"},_fetchJsonp:function(t,e){return e=e||{},f()(t,{method:"GET",timeout:e.timeout}).then(function(t){return t})},_timeout:function(t,e){return new Promise(function(r,s){setTimeout(function(){s(new Error("timeout"))},t),e.then(r,s)})},_getParameterString:function(t){var e=[];for(var r in t){var s,n=t[r];if(null!=n&&"function"!=typeof n)s=Array.isArray(n)||"[object Object]"===n.toString()?encodeURIComponent(JSON.stringify(n)):encodeURIComponent(n),e.push(encodeURIComponent(r)+"="+s)}return e.join("&")},_isMVTRequest:function(t){return t.indexOf(".mvt")>-1||t.indexOf(".pbf")>-1}};class w{constructor(t,e){this.value=t||"",this.name=e||"token",this.CLASS_NAME="SuperMap.Credential"}getUrlParameters(){return this.name+"="+this.value}getValue(){return this.value}destroy(){this.value=null,this.name=null}}w.CREDENTIAL=null;class E{static generateToken(t,e){var r=this.servers[t];if(r)return _.post(r.tokenServiceUrl,JSON.stringify(e.toJSON())).then(function(t){return t.text()})}static registerServers(t){this.servers=this.servers||{},h.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var r=t[e];this.servers[r.server]=r}}static registerToken(t,e){if(this.tokens=this.tokens||{},t&&e){var r=this._getTokenStorageKey(t);this.tokens[r]=e}}static registerKey(t,e){if(this.keys=this.keys||{},t&&!(t.length<1)&&e){t=h.isArray(t)?t:[t];for(var r=0;r<t.length;r++){var s=this._getUrlRestString(t[0])||t[0];this.keys[s]=e}}}static getServerInfo(t){return this.servers=this.servers||{},this.servers[t]}static getToken(t){if(t){this.tokens=this.tokens||{};var e=this._getTokenStorageKey(t);return this.tokens[e]}}static getKey(t){this.keys=this.keys||{};var e=this._getUrlRestString(t)||t;return this.keys[e]}static loginiServer(t,e,r,s){t=h.urlPathAppend(t,"services/security/login");var n={username:e&&e.toString(),password:r&&r.toString(),rememberme:s};n=JSON.stringify(n);return _.post(t,n,{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"}}).then(function(t){return t.json()})}static logoutiServer(t){t=h.urlPathAppend(t,"services/security/logout");return _.get(t,"",{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withoutFormatSuffix:!0}).then(function(){return!0}).catch(function(){return!1})}static loginOnline(t,e){var r=E.SSO+"/login?service="+t;this._open(r,e)}static loginiPortal(t,e,r){t=h.urlPathAppend(t,"web/login");var s={username:e&&e.toString(),password:r&&r.toString()};s=JSON.stringify(s);return _.post(t,s,{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withCredentials:!1}).then(function(t){return t.json()})}static logoutiPortal(t){t=h.urlPathAppend(t,"services/security/logout");return _.get(t,"",{headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},withCredentials:!0,withoutFormatSuffix:!0}).then(function(){return!0}).catch(function(){return!1})}static loginManager(t,e){var r=h.urlPathAppend(t,"/security/tokens"),s=e||{},n={username:s.userName&&s.userName.toString(),password:s.password&&s.password.toString()};n=JSON.stringify(n);var i=this;return _.post(r,n,{headers:{Accept:"*/*","Content-Type":"application/json; charset=UTF-8"}}).then(function(t){return t.text()}).then(function(t){return i.imanagerToken=t,t})}static destroyAllCredentials(){this.keys=null,this.tokens=null,this.servers=null}static destroyToken(t){if(t){var e=this._getTokenStorageKey(t);this.tokens=this.tokens||{},this.tokens[e]&&delete this.tokens[e]}}static destroyKey(t){if(t){this.keys=this.keys||{};var e=this._getUrlRestString(t)||t;this.keys[e]&&delete this.keys[e]}}static appendCredential(t){var e=t,r=this.getToken(t),s=r?new w(r,"token"):null;return s||(s=(r=this.getKey(t))?new w(r,"key"):null),s&&(e=h.urlAppend(e,s.getUrlParameters())),e}static _open(t,e){e=null==e||e;var r=window.screen.availWidth/2-this.INNER_WINDOW_WIDTH/2,s=window.screen.availHeight/2-this.INNER_WINDOW_HEIGHT/2,n="height="+this.INNER_WINDOW_HEIGHT+", width="+this.INNER_WINDOW_WIDTH+",top="+s+", left="+r+",toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no";e?window.open(t,"login"):window.open(t,"login",n)}static _getTokenStorageKey(t){var e=t.match(/(.*?):\/\/([^\/]+)/i);return e?e[0]:t}static _getUrlRestString(t){if(!t)return t;var e=t.match(/(http|https):\/\/(.*\/rest)/i);return e?e[0]:t}}E.INNER_WINDOW_WIDTH=600,E.INNER_WINDOW_HEIGHT=600,E.SSO="https://sso.supermap.com",E.ONLINE="https://www.supermapol.com";var x={GEOJSON:"GEOJSON",ISERVER:"ISERVER",FGB:"FGB"},C={CONTAIN:"CONTAIN",CROSS:"CROSS",DISJOINT:"DISJOINT",IDENTITY:"IDENTITY",INTERSECT:"INTERSECT",NONE:"NONE",OVERLAP:"OVERLAP",TOUCH:"TOUCH",WITHIN:"WITHIN"},O="KILOMETER",A="INCH",T="FOOT",P={CUSTOMINTERVAL:"CUSTOMINTERVAL",EQUALINTERVAL:"EQUALINTERVAL",LOGARITHM:"LOGARITHM",QUANTILE:"QUANTILE",SQUAREROOT:"SQUAREROOT",STDDEVIATION:"STDDEVIATION"},I={BLACK_WHITE:"BLACKWHITE",BLUE_BLACK:"BLUEBLACK",BLUE_RED:"BLUERED",BLUE_WHITE:"BLUEWHITE",CYAN_BLACK:"CYANBLACK",CYAN_BLUE:"CYANBLUE",CYAN_GREEN:"CYANGREEN",CYAN_WHITE:"CYANWHITE",GREEN_BLACK:"GREENBLACK",GREEN_BLUE:"GREENBLUE",GREEN_ORANGE_VIOLET:"GREENORANGEVIOLET",GREEN_RED:"GREENRED",GREEN_WHITE:"GREENWHITE",PINK_BLACK:"PINKBLACK",PINK_BLUE:"PINKBLUE",PINK_RED:"PINKRED",PINK_WHITE:"PINKWHITE",RAIN_BOW:"RAINBOW",RED_BLACK:"REDBLACK",RED_WHITE:"REDWHITE",SPECTRUM:"SPECTRUM",TERRAIN:"TERRAIN",YELLOW_BLACK:"YELLOWBLACK",YELLOW_BLUE:"YELLOWBLUE",YELLOW_GREEN:"YELLOWGREEN",YELLOW_RED:"YELLOWRED",YELLOW_WHITE:"YELLOWWHITE"},N={INDEXEDHDFS:"INDEXEDHDFS",UDB:"UDB",MONGODB:"MONGODB",PG:"PG"},R={CLIP:"clip",INTERSECT:"intersect"},M={SQUAREMETER:"SquareMeter",SQUAREKILOMETER:"SquareKiloMeter",HECTARE:"Hectare",ARE:"Are",ACRE:"Acre",SQUAREFOOT:"SquareFoot",SQUAREYARD:"SquareYard",SQUAREMILE:"SquareMile"},J={METER:"Meter",KILOMETER:"Kilometer",YARD:"Yard",FOOT:"Foot",MILE:"Mile"},L={MAX:"max",MIN:"min",AVERAGE:"average",SUM:"sum",VARIANCE:"variance",STDDEVIATION:"stdDeviation"},k={SUMMARYMESH:"SUMMARYMESH",SUMMARYREGION:"SUMMARYREGION"},D={REGIONNOOVERLAP:"REGIONNOOVERLAP",REGIONNOOVERLAPWITH:"REGIONNOOVERLAPWITH",REGIONCONTAINEDBYREGION:"REGIONCONTAINEDBYREGION",REGIONCOVEREDBYREGION:"REGIONCOVEREDBYREGION",LINENOOVERLAP:"LINENOOVERLAP",LINENOOVERLAPWITH:"LINENOOVERLAPWITH",POINTNOIDENTICAL:"POINTNOIDENTICAL"};class F{constructor(t){this.alias=null,this.connect=null,this.dataBase=null,this.driver=null,this.engineType=null,this.exclusive=null,this.OpenLinkTable=null,this.password=null,this.readOnly=null,this.server=null,this.user=null,t&&h.extend(this,t),this.CLASS_NAME="SuperMap.DatasourceConnectionInfo"}destroy(){var t=this;t.alias=null,t.connect=null,t.dataBase=null,t.driver=null,t.engineType=null,t.exclusive=null,t.OpenLinkTable=null,t.password=null,t.readOnly=null,t.server=null,t.user=null}}class U{constructor(t){this.type=N.UDB,this.datasetName="analystResult",this.datasourceInfo=null,this.outputPath="",h.extend(this,t),this.CLASS_NAME="SuperMap.OutputSetting"}destroy(){var t=this;t.type=null,t.datasetName=null,t.outputPath=null,t.datasourceInfo instanceof F&&(t.datasourceInfo.destroy(),t.datasourceInfo=null)}}class j{constructor(t){this.items=null,this.numericPrecision=1,this.rangeMode=P.EQUALINTERVAL,this.rangeCount="",this.colorGradientType=I.YELLOW_RED,h.extend(this,t),this.CLASS_NAME="SuperMap.MappingParameters"}destroy(){var t=this;if(t.items){if(t.items.length>0)for(var e in t.items)t.items[e].destroy(),t.items[e]=null;t.items=null}t.numericPrecision=null,t.rangeMode=null,t.rangeCount=null,t.colorGradientType=null}}class B{constructor(t){t&&(this.datasetName="",this.query="",this.resolution=80,this.method=0,this.meshType=0,this.fields="",this.radius=300,this.meshSizeUnit=J.METER,this.radiusUnit=J.METER,this.areaUnit=M.SQUAREMILE,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.KernelDensityJobParameter")}destroy(){this.datasetName=null,this.query=null,this.resolution=null,this.method=null,this.radius=null,this.meshType=null,this.fields=null,this.meshSizeUnit=null,this.radiusUnit=null,this.areaUnit=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class z{constructor(t){t&&(this.datasetName="",this.datasetQuery="",this.geometryQuery="",this.mode=C.CONTAIN,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.SingleObjectQueryJobsParameter")}destroy(){this.datasetName=null,this.datasetQuery=null,this.geometryQuery=null,this.mode=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class Q{constructor(t){t&&(this.datasetName="",this.groupField="",this.attributeField="",this.statisticModes="",this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.SummaryAttributesJobsParameter")}destroy(){this.datasetName=null,this.groupField=null,this.attributeField=null,this.statisticModes=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class G{constructor(t){t&&(this.datasetName="",this.regionDataset="",this.query="",this.resolution=100,this.meshType=0,this.statisticModes=L.AVERAGE,this.fields="",this.type=k.SUMMARYMESH,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.SummaryMeshJobParameter")}destroy(){this.datasetName=null,this.query=null,this.resolution=null,this.statisticModes=null,this.meshType=null,this.fields=null,this.regionDataset=null,this.type=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"type"!==r?"output"!==r?("SUMMARYMESH"===t.type&&"regionDataset"!==r||"SUMMARYREGION"===t.type&&!s(["meshType","resolution","query"],r))&&(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):e.type=t[r]:(e.input=e.input||{},e.input[r]=t[r]);function s(t,e){for(var r=t.length;r--;)if(t[r]===e)return!0;return!1}}}class q{constructor(t){t&&(this.datasetName="",this.regionDataset="",this.sumShape=!0,this.query="",this.standardSummaryFields=!1,this.standardFields="",this.standardStatisticModes="",this.weightedSummaryFields=!1,this.weightedFields="",this.weightedStatisticModes="",this.meshType=0,this.resolution=100,this.meshSizeUnit=J.METER,this.type=k.SUMMARYMESH,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.SummaryRegionJobParameter")}destroy(){this.datasetName=null,this.sumShape=null,this.regionDataset=null,this.query=null,this.standardSummaryFields=null,this.standardFields=null,this.standardStatisticModes=null,this.weightedSummaryFields=null,this.weightedFields=null,this.weightedStatisticModes=null,this.meshType=null,this.resolution=null,this.meshSizeUnit=null,this.type=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"type"!==r&&"type"!==r?"output"!==r?("SUMMARYREGION"===t.type||"SUMMARYMESH"===t.type&&"regionDataset"!==r)&&(e.analyst=e.analyst||{},"query"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):e.type=t[r]:(e.input=e.input||{},e.input[r]=t[r])}}class V{constructor(t){t&&(this.datasetName="",this.datasetOverlay="",this.mode="",this.srcFields="",this.overlayFields="",this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.OverlayGeoJobParameter")}destroy(){this.datasetName=null,this.datasetOverlay=null,this.mode=null,this.srcFields=null,this.overlayFields=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!=r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class H{constructor(t){if(this.datasetName="",this.bounds="",this.distance="",this.distanceField="",this.distanceUnit=J.METER,this.dissolveField="",this.output=null,this.mappingParameters=null,!t)return this;h.extend(this,t),this.CLASS_NAME="SuperMap.BuffersAnalystJobsParameter"}destroy(){this.datasetName=null,this.bounds=null,this.distance=null,this.distanceField=null,this.distanceUnit=null,this.dissolveField=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},"bounds"===r&&t[r]?e.analyst[r]=t[r].toBBOX():e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class K{constructor(t){t&&(this.datasetName="",this.datasetTopology="",this.tolerance="",this.rule=D.REGIONNOOVERLAP,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.TopologyValidatorJobsParameter")}destroy(){this.datasetName=null,this.datasetTopology=null,this.tolerance=null,this.rule=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}class Y{constructor(t){t.filters&&"string"==typeof t.filters&&(t.filters=t.filters.split(",")),this.address=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,h.extend(this,t)}destroy(){this.address=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null}}class W{constructor(t){t.filters&&(t.filters=t.filters.split(",")),this.x=null,this.y=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,this.geoDecodingRadius=null,h.extend(this,t)}destroy(){this.x=null,this.y=null,this.fromIndex=null,this.toIndex=null,this.filters=null,this.prjCoordSys=null,this.maxReturn=null,this.geoDecodingRadius=null}}class ${constructor(t){t=t||{},this.datasetName="",this.datasetVectorClip="",this.geometryClip="",this.mode=R.CLIP,this.output=null,this.mappingParameters=null,h.extend(this,t),this.CLASS_NAME="SuperMap.VectorClipJobsParameter"}destroy(){this.datasetName=null,this.datasetVectorClip=null,this.geometryClip=null,this.mode=null,this.output instanceof U&&(this.output.destroy(),this.output=null),this.mappingParameters instanceof j&&(this.mappingParameters.destroy(),this.mappingParameters=null)}static toObject(t,e){for(var r in t)"datasetName"!==r?"output"!==r?(e.analyst=e.analyst||{},e.analyst[r]=t[r],"mappingParameters"===r&&(e.analyst[r]=e.analyst[r]||{},e.analyst.mappingParameters=t[r])):(e.output=e.output||{},e.output=t[r]):(e.input=e.input||{},e.input[r]=t[r])}}var X=window.SuperMap=window.SuperMap||{};X.REST=X.REST||{};const Z=function(){try{return mapv}catch(t){return{}}}();var tt=function(t){var e;if(!t)return e;return["m","meter","meters"].indexOf(t.toLocaleLowerCase())>-1?e=1:["degrees","deg","degree","dd"].indexOf(t.toLocaleLowerCase())>-1?e=2*Math.PI*6378137/360:t===O?e=1e3:t===A?e=.025399999918:t===T&&(e=.3048),e};var et=Z.baiduMapLayer?Z.baiduMapLayer.__proto__:Function;class rt extends et{constructor(t,e,r,s){if(super(t,r,s),!et)return this;s=s||{},this.init(s),this.argCheck(s),this.canvasLayer=e,this.clickEvent=this.clickEvent.bind(this),this.mousemoveEvent=this.mousemoveEvent.bind(this),this.bindEvent()}clickEvent(t){var e=t.xy,r=this.devicePixelRatio||1;super.clickEvent({x:e.x/r,y:e.y/r},t)}mousemoveEvent(t){var e=t.xy;super.mousemoveEvent(e,t)}bindEvent(){var t=this.map;this.options.methods&&(this.options.methods.click&&t.events.on({click:this.clickEvent}),this.options.methods.mousemove&&t.events.on({mousemove:this.mousemoveEvent}))}unbindEvent(){var t=this.map;this.options.methods&&(this.options.methods.click&&t.events.un({click:this.clickEvent}),this.options.methods.mousemove&&t.events.un({mousemove:this.mousemoveEvent}))}getContext(){return this.canvasLayer&&this.canvasLayer.canvasContext}addData(t,e){var r=t;t&&t.get&&(r=t.get()),this.dataSet.add(r),this.update({options:e})}setData(t,e){var r=t;t&&t.get&&(r=t.get()),this.dataSet=this.dataSet||new Z.DataSet,this.dataSet.set(r),this.update({options:e})}getData(){return this.dataSet}removeData(t){if(this.dataSet){var e=this.dataSet.get({filter:function(e){return null==t||"function"!=typeof t||!t(e)}});this.dataSet.set(e),this.update({options:null})}}clearData(){this.dataSet&&this.dataSet.clear(),this.update({options:null})}render(t){this._canvasUpdate(t)}transferToMercator(){if(this.options.coordType&&["bd09mc","coordinates_mercator"].indexOf(this.options.coordType)>-1){var t=this.dataSet.get();t=this.dataSet.transferCoordinate(t,function(t){var e=X.Projection.transform({x:t[0],y:t[1]},"EPSG:3857","EPSG:4326");return[e.x,e.y]},"coordinates","coordinates"),this.dataSet._set(t)}}_canvasUpdate(t){if(this.canvasLayer){var e=this.options.animation,r=this.getContext(),s=this.map;if(this.isEnabledTime()){if(void 0===t)return void this.clear(r);"2d"===this.context&&(r.save(),r.globalCompositeOperation="destination-out",r.fillStyle="rgba(0, 0, 0, .1)",r.fillRect(0,0,r.canvas.width,r.canvas.height),r.restore())}else this.clear(r);if("2d"===this.context)for(var n in this.options)r[n]=this.options[n];else r.clear(r.COLOR_BUFFER_BIT);if(!(this.options.minZoom&&s.getZoom()<this.options.minZoom||this.options.maxZoom&&s.getZoom()>this.options.maxZoom)){var i=this.canvasLayer,o={fromColumn:"coordinates",transferCoordinate:function(t){var e={lon:t[0],lat:t[1]},r=s.getViewPortPxFromLonLat(e);return[r.x,r.y]}};void 0!==t&&(o.filter=function(r){var s=e.trails||10;return t&&r.time>t-s&&r.time<t});var a=this.dataSet.get(o);this.processData(a);var u=s.getResolution()*tt("DEGREE");"m"===this.options.unit?(this.options.size&&(this.options._size=this.options.size/u),this.options.width&&(this.options._width=this.options.width/u),this.options.height&&(this.options._height=this.options.height/u)):(this.options._size=this.options.size,this.options._height=this.options.height,this.options._width=this.options.width);var l=s.getViewPortPxFromLonLat(i.transferToMapLatLng({lon:0,lat:0}));this.drawContext(r,a,this.options,l),this.options.updateCallback&&this.options.updateCallback(t)}}}init(t){this.options=t,this.initDataRange(t),this.context=this.options.context||"2d",this.options.zIndex&&this.canvasLayer&&this.canvasLayer.setZIndex(this.options.zIndex),this.initAnimator()}addAnimatorEvent(){this.map.events.on({movestart:this.animatorMovestartEvent.bind(this)}),this.map.events.on({moveend:this.animatorMoveendEvent.bind(this)})}clear(t){t&&t.clearRect&&t.clearRect(0,0,t.canvas.width,t.canvas.height)}show(){this.map.addLayer(this.canvasLayer)}hide(){this.map.removeLayer(this.canvasLayer)}draw(){this.canvasLayer.redraw()}}X.Layer.MapVLayer=class extends X.Layer{constructor(t,e){if(super(t,e),this.dataSet=null,this.options=null,this.supported=!1,this.canvas=null,this.canvasContext=null,e&&X.Util.extend(this,e),this.canvas=document.createElement("canvas"),this.canvas.getContext){this.supported=!0,this.canvas.style.position="absolute",this.canvas.style.top="0px",this.canvas.style.left="0px",this.div.appendChild(this.canvas);var r=this.options&&this.options.context||"2d";this.canvasContext=this.canvas.getContext(r);var s="undefined"==typeof window?{}:window,n=this.devicePixelRatio=s.devicePixelRatio||1;"2d"===r&&this.canvasContext.scale(n,n),this.attribution="© 2018 百度 <a href='https://mapv.baidu.com' target='_blank'>MapV</a> with <span>© <a target='_blank' href='https://iclient.supermap.io' style='color: #08c;text-decoration: none;'>SuperMap iClient</a></span>",this.CLASS_NAME="SuperMap.Layer.MapVLayer"}}destroy(){this.renderer&&this.renderer.animator&&(this.renderer.animator.stop(),this.renderer.animator=null),this.dataSet=null,this.options=null,this.renderer=null,this.supported=null,this.canvas=null,this.canvasContext=null,this.maxWidth=null,this.maxHeight=null,super.destroy()}addData(t,e){this.renderer&&this.renderer.addData(t,e)}setData(t,e){this.renderer&&this.renderer.setData(t,e)}getData(){return this.renderer&&(this.dataSet=this.renderer.getData()),this.dataSet}removeData(t){this.renderer&&this.renderer.removeData(t)}clearData(){this.renderer.clearData()}setMap(t){super.setMap(t),this.renderer=new rt(t,this,this.dataSet,this.options),this.renderer.devicePixelRatio=this.devicePixelRatio,this.supported?this.redraw():this.map.removeLayer(this)}moveTo(t,e,r){if(super.moveTo(t,e,r),this.supported){if(this.zoomChanged=e,!r){this.div.style.visibility="hidden",this.div.style.left=-parseInt(this.map.layerContainerDiv.style.left)+"px",this.div.style.top=-parseInt(this.map.layerContainerDiv.style.top)+"px";var s=this.map.getSize();this.div.style.width=parseInt(s.w)+"px",this.div.style.height=parseInt(s.h)+"px","heatmap"===this.options.draw?(this.canvas.width=parseInt(s.w)*this.devicePixelRatio,this.canvas.height=parseInt(s.h)*this.devicePixelRatio):(this.canvas.width=parseInt(s.w),this.canvas.height=parseInt(s.h)),this.canvas.style.width=this.div.style.width,this.canvas.style.height=this.div.style.height,this.maxWidth=s.w,this.maxHeight=s.h,this.div.style.visibility="",e||this.renderer&&this.renderer.render()}e&&this.renderer&&this.renderer.render()}}transferToMapLatLng(t){var e="EPSG:4326",r=this.map.getUnits()||"degree";return["m","meter"].indexOf(r.toLowerCase())>-1&&(e="EPSG:3857"),new X.LonLat(t.lon,t.lat).transform("EPSG:4326",e)}};class st{constructor(t){this.data=null,this.keepData=!1,h.extend(this,t),this.options=t,this.CLASS_NAME="SuperMap.Format"}destroy(){}read(t){}write(t){}}class nt extends st{constructor(t){super(t),this.indent="    ",this.space=" ",this.newline="\n",this.level=0,this.pretty=!1,this.nativeJSON=!(!window.JSON||"function"!=typeof JSON.parse||"function"!=typeof JSON.stringify),this.CLASS_NAME="SuperMap.Format.JSON",this.serialize={object:function(t){if(null==t)return"null";if(t.constructor===Date)return this.serialize.date.apply(this,[t]);if(t.constructor===Array)return this.serialize.array.apply(this,[t]);var e,r,s,n=["{"];this.level+=1;var i=!1;for(e in t)t.hasOwnProperty(e)&&(r=this.write.apply(this,[e,this.pretty]),s=this.write.apply(this,[t[e],this.pretty]),null!=r&&null!=s&&(i&&n.push(","),n.push(this.writeNewline(),this.writeIndent(),r,":",this.writeSpace(),s),i=!0));return this.level-=1,n.push(this.writeNewline(),this.writeIndent(),"}"),n.join("")},array:function(t){var e,r=["["];this.level+=1;for(var s=0,n=t.length;s<n;++s)null!=(e=this.write.apply(this,[t[s],this.pretty]))&&(s>0&&r.push(","),r.push(this.writeNewline(),this.writeIndent(),e));return this.level-=1,r.push(this.writeNewline(),this.writeIndent(),"]"),r.join("")},string:function(t){var e={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};return/["\\\x00-\x1f]/.test(t)?'"'+t.replace(/([\x00-\x1f\\"])/g,function(t,r){var s=e[r];return s||(s=r.charCodeAt(),"\\u00"+Math.floor(s/16).toString(16)+(s%16).toString(16))})+'"':'"'+t+'"'},number:function(t){return isFinite(t)?String(t):"null"},boolean:function(t){return String(t)},date:function(t){function e(t){return t<10?"0"+t:t}return'"'+t.getFullYear()+"-"+e(t.getMonth()+1)+"-"+e(t.getDate())+"T"+e(t.getHours())+":"+e(t.getMinutes())+":"+e(t.getSeconds())+'"'}}}read(t,e){var r;if(this.nativeJSON)try{r=JSON.parse(t,e)}catch(e){return{data:t}}return this.keepData&&(this.data=r),r}write(t,e){this.pretty=!!e;var r=null,s=typeof t;if(this.serialize[s])try{r=!this.pretty&&this.nativeJSON?JSON.stringify(t):this.serialize[s].apply(this,[t])}catch(t){}return r}writeIndent(){var t=[];if(this.pretty)for(var e=0;e<this.level;++e)t.push(this.indent);return t.join("")}writeNewline(){return this.pretty?this.newline:""}writeSpace(){return this.pretty?this.space:""}}class it{constructor(t,e){let r=this;this.EVENT_TYPES=["processCompleted","processFailed"],this.events=null,this.eventListeners=null,this.url=null,this.urls=null,this.proxy=null,this.index=null,this.length=null,this.totalTimes=null,this.POLLING_TIMES=3,this.isInTheSameDomain=null,this.withCredentials=!1,h.isArray(t)?(r.urls=t,r.length=t.length,r.totalTimes=r.length,1===r.length?r.url=t[0]:(r.index=parseInt(Math.random()*r.length),r.url=t[r.index])):(r.totalTimes=1,r.url=t),h.isArray(t)&&!r.isServiceSupportPolling()&&(r.url=t[0],r.totalTimes=1),e=e||{},this.crossOrigin=e.crossOrigin,this.headers=e.headers,h.extend(this,e),r.isInTheSameDomain=h.isInTheSameDomain(r.url),r.events=new d(r,null,r.EVENT_TYPES,!0),r.eventListeners instanceof Object&&r.events.on(r.eventListeners),this.CLASS_NAME="SuperMap.CommonServiceBase"}destroy(){let t=this;h.isArray(t.urls)&&(t.urls=null,t.index=null,t.length=null,t.totalTimes=null),t.url=null,t.isInTheSameDomain=null,t.EVENT_TYPES=null,t.events&&(t.events.destroy(),t.events=null),t.eventListeners&&(t.eventListeners=null)}request(t){let e=t.scope.format;if("string"==typeof t.success&&(t.scope.format=t.success,e=t.success,t.success=null,t.failure=null),e&&!this.supportDataFormat(e))throw new Error(`${this.CLASS_NAME} is not surport ${e} format!`);let r=this;return t.url=t.url||r.url,this._returnContent(t)&&!t.url.includes("returnContent=true")&&(t.url=h.urlAppend(t.url,"returnContent=true")),t.proxy=t.proxy||r.proxy,t.withCredentials=void 0!=t.withCredentials?t.withCredentials:r.withCredentials,t.crossOrigin=void 0!=t.crossOrigin?t.crossOrigin:r.crossOrigin,t.headers=t.headers||r.headers,t.isInTheSameDomain=r.isInTheSameDomain,t.withoutFormatSuffix=t.scope.withoutFormatSuffix||!1,t.url=E.appendCredential(t.url),r.calculatePollingTimes(),t.scope=r,r.totalTimes>0?(r.totalTimes--,r.ajaxPolling(t)):r._commit(t)}ajaxPolling(t){let e=this,r=t.url,s=/^http:\/\/([a-z]{9}|(\d+\.){3}\d+):\d{0,4}/;return e.index=parseInt(Math.random()*e.length),e.url=e.urls[e.index],r=r.replace(s,s.exec(e.url)[0]),t.url=r,t.isInTheSameDomain=h.isInTheSameDomain(r),e._commit(t)}calculatePollingTimes(){let t=this;t.times?t.totalTimes>t.POLLING_TIMES?t.times>t.POLLING_TIMES?t.totalTimes=t.POLLING_TIMES:t.totalTimes=t.times:t.times<t.totalTimes&&(t.totalTimes=t.times):t.totalTimes>t.POLLING_TIMES&&(t.totalTimes=t.POLLING_TIMES),t.totalTimes--}isServiceSupportPolling(){return!("SuperMap.REST.ThemeService"===this.CLASS_NAME||"SuperMap.REST.EditFeaturesService"===this.CLASS_NAME)}transformResult(t,e){return{result:t=h.transformResult(t),options:e}}transformErrorResult(t,e){return{error:(t=h.transformResult(t)).error||t,options:e}}serviceProcessCompleted(t,e){t=this.transformResult(t).result,this.events.triggerEvent("processCompleted",{result:t,options:e})}serviceProcessFailed(t,e){let r=(t=this.transformErrorResult(t).error).error||t;this.events.triggerEvent("processFailed",{error:r,options:e})}_returnContent(t){return t.scope.format!==x.FGB&&!!t.scope.returnContent}supportDataFormat(t){return this.dataFormat().includes(t)}dataFormat(){return[x.GEOJSON,x.ISERVER]}_commit(t){if("POST"===t.method||"PUT"===t.method||"PATCH"===t.method)if(t.params&&(t.url=h.urlAppend(t.url,h.getParameterString(t.params||{}))),"object"!=typeof t.data||t.data instanceof FormData)t.params=t.data;else try{t.params=h.toJSON(t.data)}catch(t){console.log("不是json对象")}return _.commit(t.method,t.url,t.params,{headers:t.headers,withoutFormatSuffix:t.withoutFormatSuffix,withCredentials:t.withCredentials,crossOrigin:t.crossOrigin,timeout:t.async?0:null,proxy:t.proxy}).then(function(t){return t.text?t.text():t.json?t.json():t}).then(function(e){let r=e;return"string"==typeof e&&(r=(new nt).read(e)),(!r||r.error||r.code>=300&&304!==r.code)&&(r=r&&r.error?{error:r.error}:{error:r}),r&&t.scope.format===x.FGB&&(r.newResourceLocation=r.newResourceLocation.replace(".json","")+".fgb"),r}).catch(function(t){return{error:t}}).then(e=>{let r={object:this};if(e.error){const i="processFailed";if(this.events&&this.events.listeners[i]&&this.events.listeners[i].length){var s=t.failure&&(t.scope?n.bind(t.failure,t.scope):t.failure);s?s(e,t):this.serviceProcessFailed(e,t)}else(r={...r,...this.transformErrorResult(e,t)}).type=i,t.failure&&t.failure(r)}else{const s="processCompleted";if(this.events&&this.events.listeners[s]&&this.events.listeners[s].length){var i=t.success&&(t.scope?n.bind(t.success,t.scope):t.success);i?i(e,t):this.serviceProcessCompleted(e,t)}else e.succeed=void 0==e.succeed||e.succeed,(r={...r,...this.transformResult(e,t)}).type=s,t.success&&t.success(r)}return r})}}class ot extends it{constructor(t,e){super(t,e),this.options=e||{},this.CLASS_NAME="SuperMap.AddressMatchService"}destroy(){super.destroy()}code(t,e,r){if(e instanceof Y)return this.processAsync(t,e,r)}decode(t,e,r){if(e instanceof W)return this.processAsync(t,e,r)}processAsync(t,e,r){return this.request({method:"GET",url:t,params:e,scope:this,success:r,failure:r})}transformResult(t,e){return t.succeed&&delete t.succeed,{result:t,options:e}}}X.REST.AddressMatchService=class extends it{constructor(t,e){super(t,e),this.CLASS_NAME="SuperMap.REST.AddressMatchService"}code(t,e){var r=this;return new ot(r.url,{headers:r.headers,proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin}).code(r.url+"/geocoding",t,e)}decode(t,e){var r=this;return new ot(r.url,{headers:r.headers,proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin}).decode(r.url+"/geodecoding",t,e)}};class at extends it{constructor(t,e){super(t,e),e&&(this.datasource=null,this.dataset=null,e&&h.extend(this,e),this.CLASS_NAME="SuperMap.DatasetService")}destroy(){super.destroy();this.datasource=null,this.dataset=null}getDatasetsService(t,e){const r=h.urlPathAppend(this.url,`datasources/name/${t}/datasets`);return this.processAsync(r,"GET",e)}getDatasetService(t,e,r){const s=h.urlPathAppend(this.url,`datasources/name/${t}/datasets/name/${e}`);return this.processAsync(s,"GET",r)}setDatasetService(t,e){if(!t)return;const r=h.urlPathAppend(this.url,`datasources/name/${t.datasourceName}/datasets/name/${t.datasetName}`);return delete t.datasourceName,this.processAsync(r,"PUT",e,t)}deleteDatasetService(t,e,r){const s=h.urlPathAppend(this.url,`datasources/name/${t}/datasets/name/${e}`);return this.processAsync(s,"DELETE",r)}processAsync(t,e,r,s){let n={url:t,method:e,scope:this,success:r,failure:r};return s&&(n.data=h.toJSON(s)),this.request(n)}}class ut{constructor(t){t&&(this.datasourceName=null,this.datasetName=null,this.datasetType=null,t&&h.extend(this,t),this.CLASS_NAME="SuperMap.CreateDatasetParameters")}destroy(){this.datasourceName=null,this.datasetName=null,this.datasetType=null}}class lt{constructor(t){t&&(this.datasourceName=null,this.datasetName=null,this.isFileCache=null,this.description=null,this.prjCoordSys=null,this.charset=null,this.palette=null,this.noValue=null,t&&h.extend(this,t),this.CLASS_NAME="SuperMap.UpdateDatasetParameters")}destroy(){var t=this;t.datasourceName=null,t.datasetName=null,t.isFileCache=null,t.prjCoordSys=null,t.charset=null,t.palette=null,t.noValue=null}}X.REST.DatasetService=class extends it{constructor(t,e){super(t,e);const r=this;this._datasetService=new at(r.url,{proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin,headers:r.headers}),this.CLASS_NAME="SuperMap.REST.DatasetService"}getDatasets(t,e){if(t)return this._datasetService.getDatasetsService(t,e)}getDataset(t,e,r){if(t&&e)return this._datasetService.getDatasetService(t,e,r)}setDataset(t,e){if(!(t instanceof ut||t instanceof lt))return;let r;return t instanceof ut?r={datasetType:t.datasetType,datasourceName:t.datasourceName,datasetName:t.datasetName}:t instanceof lt&&(r={datasetName:t.datasetName,datasourceName:t.datasourceName,isFileCache:t.isFileCache,description:t.description,prjCoordSys:t.prjCoordSys,charset:t.charset}),this._datasetService.setDatasetService(r,e)}deleteDataset(t,e,r){return this._datasetService.deleteDatasetService(t,e,r)}};class ht extends it{constructor(t,e){super(t,e),e&&h.extend(this,e),this.CLASS_NAME="SuperMap.DatasourceService"}destroy(){super.destroy()}getDatasourceService(t,e){let r=h.urlPathAppend(this.url,`datasources/name/${t}`);return this.processAsync(r,"GET",e)}getDatasourcesService(t){let e=h.urlPathAppend(this.url,"datasources");return this.processAsync(e,"GET",t)}setDatasourceService(t,e){if(!t)return;const r=h.urlPathAppend(this.url,`datasources/name/${t.datasourceName}`);return this.processAsync(r,"PUT",e,t)}processAsync(t,e,r,s){let n={url:t,method:e,scope:this,success:r,failure:r};return s&&(n.data=h.toJSON(s)),this.request(n)}}class ct{constructor(t){t&&(this.datasourceName=null,this.description=null,this.coordUnit=null,this.distanceUnit=null,t&&h.extend(this,t),this.CLASS_NAME="SuperMap.SetDatasourceParameters")}destroy(){this.datasourceName=null,this.description=null,this.coordUnit=null,this.distanceUnit=null}}X.REST.DatasourceService=class extends it{constructor(t,e){super(t,e);const r=this;this._datasourceService=new ht(r.url,{proxy:r.proxy,withCredentials:r.withCredentials,crossOrigin:r.crossOrigin,headers:r.headers}),this.CLASS_NAME="SuperMap.REST.DatasourceService"}getDatasources(t){return this._datasourceService.getDatasourcesService(t)}getDatasource(t,e){if(t)return this._datasourceService.getDatasourceService(t,e)}setDatasource(t,e){if(!(t instanceof ct))return;const r={description:t.description,coordUnit:t.coordUnit,distanceUnit:t.distanceUnit,datasourceName:t.datasourceName};return this._datasourceService.setDatasourceService(r,e)}};class pt extends it{constructor(t,e){super(t,e=e||{}),this.CLASS_NAME="SuperMap.ProcessingServiceBase"}destroy(){super.destroy()}getJobs(t,e){var r=this;return _.get(E.appendCredential(t),null,{proxy:r.proxy}).then(function(t){return t.json()}).then(function(t){const s={result:t,object:r,type:"processCompleted"};return e(s),s}).catch(function(t){const s={error:t,object:r,type:"processFailed"};return e(s),s})}addJob(t,e,r,s,n,i){var o=this,a=null;e&&e instanceof r&&(a=new Object,r.toObject(e,a));let u=Object.assign({"Content-Type":"application/x-www-form-urlencoded"},o.headers||{});var l={proxy:o.proxy,headers:u,withCredentials:o.withCredentials,crossOrigin:o.crossOrigin,isInTheSameDomain:o.isInTheSameDomain};return _.post(E.appendCredential(t),JSON.stringify(a),l).then(function(t){return t.json()}).then(function(t){return t.succeed?o.transformResult(t,s,n,i):((t=o.transformErrorResult(t)).options=o,t.type="processFailed",n(t),t)}).catch(function(t){return(t=o.transformErrorResult({error:t})).options=o,t.type="processFailed",n(t),t})}transformResult(t,e,r,s){t=h.transformResult(t),e=e||1e3;var n=this;if(t)return new Promise(i=>{var o=setInterval(function(){_.get(E.appendCredential(t.newResourceLocation),{_t:(new Date).getTime()}).then(function(t){return t.json()}).then(function(t){if(i({object:n,id:t.id,state:t.state}),s({id:t.id,state:t.state,object:n}),"LOST"===t.state.runState||"KILLED"===t.state.runState||"FAILED"===t.state.runState){clearInterval(o);const e={error:t.state.errorMsg,state:t.state.runState,object:n,type:"processFailed"};i(e),r(e)}if("FINISHED"===t.state.runState&&t.setting.serviceInfo){clearInterval(o);const e={result:t,object:n,type:"processCompleted"};i(e),r(e)}}).catch(function(t){clearInterval(o);const e={error:t,object:n,type:"processFailed"};i(e),r(e)})},e)})}}class dt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/density"),this.CLASS_NAME="SuperMap.KernelDensityJobsService"}destroy(){super.destroy()}getKernelDensityJobs(t){return super.getJobs(this.url,t)}getKernelDensityJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addKernelDensityJob(t,e,r,s){return super.addJob(this.url,t,B,e,r,s)}}class mt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/query"),this.CLASS_NAME="SuperMap.SingleObjectQueryJobsService"}destroy(){super.destroy()}getQueryJobs(t){return super.getJobs(this.url,t)}getQueryJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addQueryJob(t,e,r,s){return super.addJob(this.url,t,z,e,r,s)}}class ft extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/aggregatepoints"),this.CLASS_NAME="SuperMap.SummaryMeshJobsService"}destroy(){super.destroy()}getSummaryMeshJobs(t){return super.getJobs(this.url,t)}getSummaryMeshJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addSummaryMeshJob(t,e,r,s){return super.addJob(this.url,t,G,e,r,s)}}class gt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/vectorclip"),this.CLASS_NAME="SuperMap.VectorClipJobsService"}destroy(){super.destroy()}getVectorClipJobs(t){return super.getJobs(this.url,t)}getVectorClipJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addVectorClipJob(t,e,r,s){return super.addJob(this.url,t,$,e,r,s)}}class yt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/overlay"),this.CLASS_NAME="SuperMap.OverlayGeoJobsService"}destroy(){super.destroy()}getOverlayGeoJobs(t){return super.getJobs(this.url,t)}getOverlayGeoJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addOverlayGeoJob(t,e,r,s){return super.addJob(this.url,t,V,e,r,s)}}class vt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/summaryregion"),this.CLASS_NAME="SuperMap.SummaryRegionJobsService"}destroy(){super.destroy()}getSummaryRegionJobs(t){return super.getJobs(this.url,t)}getSummaryRegionJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addSummaryRegionJob(t,e,r,s){return super.addJob(this.url,t,q,e,r,s)}}class bt extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/buffers"),this.CLASS_NAME="SuperMap.BuffersAnalystJobsService"}destroy(){super.destroy()}getBuffersJobs(t){return super.getJobs(this.url,t)}getBuffersJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addBuffersJob(t,e,r,s){return super.addJob(this.url,t,H,e,r,s)}}class St extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/topologyvalidator"),this.CLASS_NAME="SuperMap.TopologyValidatorJobsService"}destroy(){super.destroy()}getTopologyValidatorJobs(t){return super.getJobs(this.url,t)}getTopologyValidatorJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addTopologyValidatorJob(t,e,r,s){return super.addJob(this.url,t,K,e,r,s)}}class _t extends pt{constructor(t,e){super(t,e),this.url=h.urlPathAppend(this.url,"spatialanalyst/summaryattributes"),this.CLASS_NAME="SuperMap.SummaryAttributesJobsService"}destroy(){super.destroy()}getSummaryAttributesJobs(t){return super.getJobs(this.url,t)}getSummaryAttributesJob(t,e){return super.getJobs(h.urlPathAppend(this.url,t),e)}addSummaryAttributesJob(t,e,r,s){return super.addJob(this.url,t,Q,e,r,s)}}class wt{constructor(t,e){this.url=t,this.options=e||{},this.kernelDensityJobs={},this.summaryMeshJobs={},this.queryJobs={},this.summaryRegionJobs={},this.vectorClipJobs={},this.overlayGeoJobs={},this.buffersJobs={},this.topologyValidatorJobs={},this.summaryAttributesJobs={}}getKernelDensityJobs(t,e){var r=this,s=r._processFormat(e);return new dt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getKernelDensityJobs(t)}getKernelDensityJob(t,e,r){var s=this,n=s._processFormat(r);return new dt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getKernelDensityJob(t,e)}addKernelDensityJob(t,e,r,s){var n=this,i=n._processFormat(s);return new dt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addKernelDensityJob(t,r,e,function(t){n.kernelDensityJobs[t.id]=t.state})}getKernelDensityJobState(t){return this.kernelDensityJobs[t]}getSummaryMeshJobs(t,e){var r=this,s=r._processFormat(e);return new ft(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getSummaryMeshJobs(t)}getSummaryMeshJob(t,e,r){var s=this,n=s._processFormat(r);return new ft(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getSummaryMeshJob(t,e)}addSummaryMeshJob(t,e,r,s){var n=this,i=n._processFormat(s);return new ft(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addSummaryMeshJob(t,r,e,function(t){n.summaryMeshJobs[t.id]=t.state})}getSummaryMeshJobState(t){return this.summaryMeshJobs[t]}getQueryJobs(t,e){var r=this,s=r._processFormat(e);return new mt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getQueryJobs(t)}getQueryJob(t,e,r){var s=this,n=s._processFormat(r);return new mt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getQueryJob(t,e)}addQueryJob(t,e,r,s){var n=this,i=n._processFormat(s);return new mt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addQueryJob(t,r,e,function(t){n.queryJobs[t.id]=t.state})}getQueryJobState(t){return this.queryJobs[t]}getSummaryRegionJobs(t,e){var r=this,s=r._processFormat(e);return new vt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getSummaryRegionJobs(t)}getSummaryRegionJob(t,e,r){var s=this,n=s._processFormat(r);return new vt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getSummaryRegionJob(t,e)}addSummaryRegionJob(t,e,r,s){var n=this,i=n._processFormat(s);return new vt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addSummaryRegionJob(t,r,e,function(t){n.summaryRegionJobs[t.id]=t.state})}getSummaryRegionJobState(t){return this.summaryRegionJobs[t]}getVectorClipJobs(t,e){var r=this,s=r._processFormat(e);return new gt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getVectorClipJobs(t)}getVectorClipJob(t,e,r){var s=this,n=s._processFormat(r);return new gt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getVectorClipJob(t,e)}addVectorClipJob(t,e,r,s){var n=this,i=n._processFormat(s);return new gt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addVectorClipJob(t,r,e,function(t){n.vectorClipJobs[t.id]=t.state})}getVectorClipJobState(t){return this.vectorClipJobs[t]}getOverlayGeoJobs(t,e){var r=this,s=r._processFormat(e);return new yt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getOverlayGeoJobs(t)}getOverlayGeoJob(t,e,r){var s=this,n=s._processFormat(r);return new yt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getOverlayGeoJob(t,e)}addOverlayGeoJob(t,e,r,s){var n=this,i=n._processFormat(s);return new yt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addOverlayGeoJob(t,r,e,function(t){n.overlayGeoJobs[t.id]=t.state})}getoverlayGeoJobState(t){return this.overlayGeoJobs[t]}getBuffersJobs(t,e){var r=this,s=r._processFormat(e);return new bt(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getBuffersJobs(t)}getBuffersJob(t,e,r){var s=this,n=s._processFormat(r);return new bt(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getBuffersJob(t,e)}addBuffersJob(t,e,r,s){var n=this,i=n._processFormat(s);return new bt(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addBuffersJob(t,r,e,function(t){n.buffersJobs[t.id]=t.state})}getBuffersJobState(t){return this.buffersJobs[t]}getTopologyValidatorJobs(t,e){var r=this,s=r._processFormat(e);return new St(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getTopologyValidatorJobs(t)}getTopologyValidatorJob(t,e,r){var s=this,n=s._processFormat(r);return new St(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getTopologyValidatorJob(t,e)}addTopologyValidatorJob(t,e,r,s){var n=this,i=n._processFormat(s);return new St(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addTopologyValidatorJob(t,r,e,function(t){n.topologyValidatorJobs[t.id]=t.state})}getTopologyValidatorJobState(t){return this.topologyValidatorJobs[t]}getSummaryAttributesJobs(t,e){var r=this,s=r._processFormat(e);return new _t(r.url,{proxy:r.options.proxy,withCredentials:r.options.withCredentials,crossOrigin:r.options.crossOrigin,headers:r.options.headers,format:s}).getSummaryAttributesJobs(t)}getSummaryAttributesJob(t,e,r){var s=this,n=s._processFormat(r);return new _t(s.url,{proxy:s.options.proxy,withCredentials:s.options.withCredentials,crossOrigin:s.options.crossOrigin,headers:s.options.headers,format:n}).getSummaryAttributesJob(t,e)}addSummaryAttributesJob(t,e,r,s){var n=this,i=n._processFormat(s);return new _t(n.url,{proxy:n.options.proxy,withCredentials:n.options.withCredentials,crossOrigin:n.options.crossOrigin,headers:n.options.headers,format:i}).addSummaryAttributesJob(t,r,e,function(t){n.summaryAttributesJobs[t.id]=t.state})}getSummaryAttributesJobState(t){return this.summaryAttributesJobs[t]}_processFormat(t){return t||x.GEOJSON}}X.REST.ProcessingService=class{constructor(t,e){this._processingService=new wt(t,e)}getKernelDensityJobs(t,e){return this._processingService.getKernelDensityJobs(t,e)}getKernelDensityJob(t,e,r){return this._processingService.getKernelDensityJob(t,e,r)}addKernelDensityJob(t,e,r,s){return t=this._processParams(t),this._processingService.addKernelDensityJob(t,e,r,s)}getKernelDensityJobState(t){return this._processingService.getKernelDensityJobState(t)}getSummaryMeshJobs(t,e){return this._processingService.getSummaryMeshJobs(t,e)}getSummaryMeshJob(t,e,r){return this._processingService.getSummaryMeshJob(t,e,r)}addSummaryMeshJob(t,e,r,s){return t=this._processParams(t),this._processingService.addSummaryMeshJob(t,e,r,s)}getSummaryMeshJobState(t){return this._processingService.getSummaryMeshJobState(t)}getQueryJobs(t,e){return this._processingService.getQueryJobs(t,e)}getQueryJob(t,e,r){return this._processingService.getQueryJob(t,e,r)}addQueryJob(t,e,r,s){return t=this._processParams(t),this._processingService.addQueryJob(t,e,r,s)}getQueryJobState(t){return this._processingService.getQueryJobState(t)}getSummaryRegionJobs(t,e){return this._processingService.getSummaryRegionJobs(t,e)}getSummaryRegionJob(t,e,r){return this._processingService.getSummaryRegionJob(t,e,r)}addSummaryRegionJob(t,e,r,s){return t=this._processParams(t),this._processingService.addSummaryRegionJob(t,e,r,s)}getSummaryRegionJobState(t){return this._processingService.getSummaryRegionJobState(t)}getVectorClipJobs(t,e){return this._processingService.getVectorClipJobs(t,e)}getVectorClipJob(t,e,r){return this._processingService.getVectorClipJob(t,e,r)}addVectorClipJob(t,e,r,s){return t=this._processParams(t),this._processingService.addVectorClipJob(t,e,r,s)}getVectorClipJobState(t){return this._processingService.getVectorClipJobState(t)}getOverlayGeoJobs(t,e){return this._processingService.getOverlayGeoJobs(t,e)}getOverlayGeoJob(t,e,r){return this._processingService.getOverlayGeoJob(t,e,r)}addOverlayGeoJob(t,e,r,s){return t=this._processParams(t),this._processingService.addOverlayGeoJob(t,e,r,s)}getoverlayGeoJobState(t){return this._processingService.getoverlayGeoJobState(t)}getBuffersJobs(t,e){return this._processingService.getBuffersJobs(t,e)}getBuffersJob(t,e,r){return this._processingService.getBuffersJob(t,e,r)}addBuffersJob(t,e,r,s){return t=this._processParams(t),this._processingService.addBuffersJob(t,e,r,s)}getBuffersJobState(t){return this._processingService.getBuffersJobState(t)}getTopologyValidatorJobs(t,e){return this._processingService.getTopologyValidatorJobs(t,e)}getTopologyValidatorJob(t,e,r){return this._processingService.getTopologyValidatorJob(t,e,r)}addTopologyValidatorJob(t,e,r,s){return t=this._processParams(t),this._processingService.addTopologyValidatorJob(t,e,r,s)}getTopologyValidatorJobState(t){return this._processingService.getTopologyValidatorJobState(t)}getSummaryAttributesJobs(t,e){return this._processingService.getSummaryAttributesJobs(t,e)}getSummaryAttributesJob(t,e,r){return this._processingService.getSummaryAttributesJob(t,e,r)}addSummaryAttributesJob(t,e,r,s){return t=this._processParams(t),this._processingService.addSummaryAttributesJob(t,e,r,s)}getSummaryAttributesJobState(t){return this._processingService.getSummaryAttributesJobState(t)}_processFormat(t){return t||x.GEOJSON}_processParams(t){return t?(t.geometryQuery&&(t.geometryQuery=this._convertPatams(t.geometryQuery)),t.geometryClip&&(t.geometryClip=this._convertPatams(t.geometryClip)),t):{}}_convertPatams(t){var e={};if(t.length<1)e="";else{for(var r=[],s=0;s<t.length;s++){var n={};n.x=t[s].x,n.y=t[s].y,r.push(n)}e.type="REGION",e.points=r}return e}},X.ElasticSearch=class{constructor(t,e,r){if(!e||"function"!=typeof e&&"object"!=typeof e||"function"!=typeof e.Client)throw Error("Please enter the global variable of @elastic/elasticsearch@5.6.22 or elasticsearch@16.7.3 for the second parameter!");r=r||{},this.url=t;try{this.client=new e.Client({host:this.url})}catch(t){this.client=new e.Client({node:{url:new URL(this.url)}})}this.change=null,this.openGeoFence=!1,this.outOfGeoFence=null,this.geoFence=null,this.EVENT_TYPES=["change","error","outOfGeoFence"],this.events=new d(this,null,this.EVENT_TYPES),this.eventListeners=null,h.extend(this,r),this.eventListeners instanceof Object&&this.events.on(this.eventListeners)}setGeoFence(t){this.geoFence=t}bulk(t,e){return this.client.bulk(t,this._handleCallback(e))}clearScroll(t,e){return this.client.clearScroll(t,this._handleCallback(e))}count(t,e){return this.client.count(t,this._handleCallback(e))}create(t,e){return this.client.create(t,this._handleCallback(e))}delete(t,e){return this.client.delete(t,this._handleCallback(e))}deleteByQuery(t,e){return this.client.deleteByQuery(t,this._handleCallback(e))}deleteScript(t,e){return this.client.deleteScript(t,this._handleCallback(e))}deleteTemplate(t,e){return this.client.deleteTemplate(t,this._handleCallback(e))}exists(t,e){return this.client.exists(t,this._handleCallback(e))}existsSource(t,e){return this.client.existsSource(t,this._handleCallback(e))}explain(t,e){return this.client.explain(t,this._handleCallback(e))}fieldCaps(t,e){return this.client.fieldCaps(t,this._handleCallback(e))}get(t,e){return this.client.get(t,this._handleCallback(e))}getScript(t,e){return this.client.getScript(t,this._handleCallback(e))}getSource(t,e){return this.client.getSource(t,this._handleCallback(e))}getTemplate(t,e){return this.client.getTemplate(t,this._handleCallback(e))}index(t,e){return this.client.index(t,this._handleCallback(e))}info(t,e){return this.client.info(t,this._handleCallback(e))}mget(t,e){return this.client.mget(t,this._handleCallback(e))}msearch(t,e){let r=this;return r.client.msearch(t).then(function(t){return t=t.body||t,r._update(t.responses,e),t},function(t){return e(t),r.events.triggerEvent("error",{error:t}),t})}msearchTemplate(t,e){return this.client.msearchTemplate(t,this._handleCallback(e))}mtermvectors(t,e){return this.client.mtermvectors(t,this._handleCallback(e))}ping(t,e){return this.client.ping(t,this._handleCallback(e))}putScript(t,e){return this.client.putScript(t,this._handleCallback(e))}putTemplate(t,e){return this.client.putTemplate(t,this._handleCallback(e))}reindex(t,e){return this.client.reindex(t,this._handleCallback(e))}reindexRessrottle(t,e){return this.client.reindexRessrottle(t,this._handleCallback(e))}renderSearchTemplate(t,e){return this.client.renderSearchTemplate(t,this._handleCallback(e))}scroll(t,e){return this.client.scroll(t,this._handleCallback(e))}search(t,e){let r=this;return r.client.search(t).then(function(t){return t=t.body||t,r._update(t,e),t},function(t){return e&&e(t),r.events.triggerEvent("error",{error:t}),t})}searchShards(t,e){return this.client.searchShards(t,this._handleCallback(e))}searchTemplate(t,e){return this.client.searchTemplate(t,this._handleCallback(e))}suggest(t,e){return this.client.suggest(t,this._handleCallback(e))}termvectors(t,e){return this.client.termvectors(t,this._handleCallback(e))}update(t,e){return this.client.update(t,this._handleCallback(e))}updateByQuery(t,e){return this.client.updateByQuery(t,this._handleCallback(e))}_handleCallback(t){return function(){let e=Array.from(arguments);const r=e.shift();let s=e.shift();const n=s&&s.body;if(n){const{statusCode:t,headers:r}=s;e=[t,r],s=n}t.call(this,r,s,...e)}}_update(t,e){let r=this;t&&(r.data=t,r.openGeoFence&&r.geoFence&&r._validateDatas(t),r.events.triggerEvent("change",{data:r.data}),r.change?r.change&&r.change(t):e&&e(void 0,{responses:t}))}_validateDatas(t){if(t){t instanceof Array||(t=[t]);var e,r=t.length;for(e=0;e<r;e++)this._validateData(t[e])}}_validateData(t){let e=this;t.hits.hits.map(function(r){let s=r._source,n=e._getMeterPerMapUnit(e.geoFence.unit),i=e.geoFence.center[0]*n,o=e.geoFence.center[1]*n,a=s.x*n,u=s.y*n;return e._distance(a,u,i,o)>e.geoFence.radius&&(e.outOfGeoFence&&e.outOfGeoFence(t),e.events.triggerEvent("outOfGeoFence",{data:t})),r})}_distance(t,e,r,s){return Math.sqrt((t-r)*(t-r)+(e-s)*(e-s))}_getMeterPerMapUnit(t){let e;return"meter"===t?e=1:"degree"===t&&(e=2*Math.PI*6378137/360),e}},X.SecurityManager=E,X.VectorClipJobsParameter=$,X.KernelDensityJobParameter=B,X.SingleObjectQueryJobsParameter=z,X.SummaryAttributesJobsParameter=Q,X.SummaryMeshJobParameter=G,X.SummaryRegionJobParameter=q,X.OverlayGeoJobParameter=V,X.BuffersAnalystJobsParameter=H,X.TopologyValidatorJobsParameter=K,X.OutputSetting=U,X.MappingParameters=j,X.GeoCodingParameter=Y,X.GeoDecodingParameter=W,X.FetchRequest=_,X.Util={...X.Util,...h}})()})();