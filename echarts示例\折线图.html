<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>ECharts 折线图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <style>
      #chart {
        width: 800px;
        height: 400px;
      }
    </style>
  </head>
  <body>
    <div id="chart"></div>
    <script>
      var chartDom = document.getElementById('chart')
      var myChart = echarts.init(chartDom)

      var option = {
        title: {
          text: '流量监测',
          left: 'center',
          textStyle: {
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let tooltip = `${params[0].axisValue}<br/>`
            params.forEach((param) => {
              tooltip += `${param.marker} ${param.seriesName}: ${param.data} 辆<br/>`
            })
            return tooltip
          },
        },
        legend: {
          data: ['宜兴方向', '马山方向'],
          top: '10%',
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '20%',
          bottom: '15%',
          backGround: '#ff0000',
        },
        xAxis: {
          type: 'category',
          data: ['7:00', '8:00', '9:00', '10:00', '11:00', '12:00', '13:00'],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位：辆',
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        series: [
          {
            name: '宜兴方向',
            type: 'line',
            smooth: true,
            data: [1000, 1235, 1500, 2000, 1800, 1600, 2200],
            lineStyle: {
              color: '#37A2DA',
            },
            itemStyle: {
              color: '#37A2DA',
            },
          },
          {
            name: '马山方向',
            type: 'line',
            smooth: true,
            data: [800, 1020, 1300, 1700, 1500, 1400, 2000],
            lineStyle: {
              color: '#FF9F7F',
            },
            itemStyle: {
              color: '#FF9F7F',
            },
          },
        ],
        graphic: [
          // 背景矩形配置
          {
            type: 'rect',
            left: '10%', // 第一列背景起点
            top: '20%', // 背景起点
            shape: {
              x: 0,
              y: 0,
              width: '11%', // 背景宽度
              height: '60%', // 背景高度
            },
            style: {
              fill: 'rgba(255, 255, 0, 1)',
            },
          },
          {
            type: 'rect',
            left: '31%', // 第二列背景起点
            top: '20%',
            shape: {
              x: 0,
              y: 0,
              width: '11%',
              height: '60%',
            },
            style: {
              fill: 'rgba(255, 255, 0, 1)',
            },
          },
          {
            type: 'rect',
            left: '52%', // 第三列背景起点
            top: '20%',
            shape: {
              x: 0,
              y: 0,
              width: '11%',
              height: '60%',
            },
            style: {
              fill: 'rgba(255, 255, 0, 1)',
            },
          },
          {
            type: 'rect',
            left: '73%', // 第四列背景起点
            top: '20%',
            shape: {
              x: 0,
              y: 0,
              width: '11%',
              height: '60%',
            },
            style: {
              fill: 'rgba(255, 255, 0, 1)',
            },
          },
        ],
      }

      myChart.setOption(option)
    </script>
  </body>
</html>
